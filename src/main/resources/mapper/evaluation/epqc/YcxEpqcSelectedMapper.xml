<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.epqc.mapper.YcxEpqcSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.epqc.model.YcxEpqcSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="epqa_id" jdbcType="INTEGER" property="epqaId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
    <select id="getSelecteds" resultMap="BaseResultMap">
      select sele.*,
      epqc.content,
      epqc.order_num,
      op.name as optionName
      from
        ycx_epqc_selected as sele
          inner join
        ycx_epqc as epqc
        on
          epqc.id = sele.epqa_id
          left join
        ycx_epqc_option as op
        on
            op.epqc_id = epqc.id
            and
            op.value = sele.answer
      where
        sele.record_id = #{recordId}
      order by epqc.order_num asc
    </select>
</mapper>