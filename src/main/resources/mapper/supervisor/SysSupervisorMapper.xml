<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.supervisor.mapper.SysSupervisorMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.supervisor.model.SysSupervisor">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_inside" jdbcType="INTEGER" property="isInside" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
    <select id="getAbaSupervisorCourseCount" resultType="com.bmh.project.dashboard.vo.SupervisorRankVo">
        SELECT ss.`name` AS supervisionName,
        ss.id AS supervisionId,
        COUNT(apph.id) AS supervisionProjectCount
        FROM sys_supervisor ss
        INNER JOIN sys_supervisor_scope sss ON ss.id = sss.supervisor_id
        INNER JOIN (
        select a.id,a.update_user,a.create_time,b.org_id from aba_plan_project_history a
        INNER JOIN aba_plan b on a.plan_id = b.id
        where b.org_id = #{orgId}
        ) apph ON ss.`name` = apph.update_user and apph.org_id = #{orgId}
        WHERE sss.org_id = #{orgId}
        AND apph.create_time &gt; #{startTime}
        AND apph.create_time &lt; #{endTime}
        AND ss.`status` = 1
        <if test="removeSupervisorList != null and removeSupervisorList.size() > 0">
            AND ss.id NOT IN
            <foreach collection="removeSupervisorList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY ss.id
    </select>
    <select id="getStSupervisorCourseCount" resultType="com.bmh.project.dashboard.vo.SupervisorRankVo">
        SELECT ss.`name` AS supervisionName,
        ss.id AS supervisionId,
        COUNT(spah.id) AS supervisionProjectCount
        FROM sys_supervisor ss
        INNER JOIN sys_supervisor_scope sss ON ss.id = sss.supervisor_id
        INNER JOIN (
            select a.id,a.update_user,a.create_time,b.org_id from st_plan_activity_history a
            left join  st_plan b on a.plan_id = b.id where b.org_id = #{orgId}
        ) spah ON ss.`name` = spah.update_user and spah.org_id = #{orgId}
        WHERE spah.create_time &gt; #{startTime}
        AND spah.create_time &lt; #{endTime}
        AND sss.org_id = #{orgId}
        AND ss.`status` = 1
        <if test="removeSupervisorList != null and removeSupervisorList.size() > 0">
            AND ss.id NOT IN
            <foreach collection="removeSupervisorList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY ss.id
    </select>
    <select id="getOrgAbaSupervisorCourseCount" resultType="com.bmh.project.dashboard.vo.SupervisorRankVo">
        SELECT
        so.`short_name` AS supervisionName,
        app.org_id AS supervisionId,
        COUNT(apph.id) AS supervisionProjectCount
        FROM
        aba_plan_project_history apph
        INNER JOIN aba_plan_project app ON apph.plan_project_id = app.id
        INNER JOIN sys_org so ON app.org_id = so.id
        INNER JOIN sys_supervisor ss ON apph.update_user = ss.`name`
        WHERE
        apph.create_time &gt; #{startTime}
        AND apph.create_time &lt; #{endTime}
        AND app.org_id NOT IN ( 1, 19, 43, 65 )
        AND apph.update_user NOT IN ( '杨春玉', '刘洁', '张凤宇', '吴东飞', '鲁海峰', '督导体验', '曾博士' )
        GROUP BY
        app.org_id
    </select>
    <select id="getOrgStSupervisorCourseCount" resultType="com.bmh.project.dashboard.vo.SupervisorRankVo">
        SELECT
        so.`short_name` AS supervisionName,
        spa.org_id AS supervisionId,
        COUNT(spah.id) AS supervisionProjectCount
        FROM
        st_plan_activity_history spah
        INNER JOIN st_plan_activity spa ON spah.plan_activity_id = spa.id
        INNER JOIN sys_org so ON spa.org_id = so.id
        INNER JOIN sys_supervisor ss ON spah.update_user = ss.`name`
        WHERE
        spah.create_time &gt; #{startTime}
        AND spah.create_time &lt; #{endTime}
        AND spa.org_id NOT IN ( 1, 19, 43, 65 )
        AND spah.update_user NOT IN ( '杨春玉', '刘洁', '张凤宇', '吴东飞', '鲁海峰', '督导体验', '曾博士' )
        GROUP BY
        spa.org_id
    </select>
    <select id="selectOrgSupervisorCount" resultType="java.util.Map">
        select
            org_id as orgId,
            COUNT(supervisor_id) as count
        from sys_supervisor_scope
        where supervisor_id not in (1, 2, 3, 8, 9, 10, 21)
        group by org_id
    </select>

</mapper>
