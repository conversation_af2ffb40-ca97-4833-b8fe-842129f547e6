<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.book.mapper.YcxChildrenBookMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.book.model.YcxChildrenBook">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="doctor_id" jdbcType="INTEGER" property="doctorId" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="book_date" jdbcType="TIMESTAMP" property="bookDate" />
    <result column="book_begin_time" jdbcType="TIMESTAMP" property="bookBeginTime" />
    <result column="book_end_time" jdbcType="TIMESTAMP" property="bookEndTime" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="month_age" jdbcType="INTEGER" property="monthAge" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="logo" jdbcType="VARCHAR" property="logo" />
    <result column="gender" jdbcType="INTEGER" property="gender" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="birthday" jdbcType="TIMESTAMP" property="birthday" />
    <result column="is_city" jdbcType="INTEGER" property="isCity" />
    <result column="creator_id" jdbcType="INTEGER" property="creatorId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <!--获取医生可预约的时间段-->
  <select id="getDoctorTimes" resultType="com.bmh.project.book.vo.DoctorTimesVo">
    SELECT bt.type,bt.book_begin_time AS bookBeginTime,bt.book_end_time AS bookEndTime,
           DATE_FORMAT(bt.book_begin_time,'%H:%i') AS showTime,
           IF(b.id IS NULL
                  AND DATE_FORMAT(CONCAT(DATE_FORMAT(#{bookDate},'%Y-%m-%d'),' ',bt.book_begin_time),'%Y-%m-%d %H:%i')>DATE_FORMAT(NOW(),'%Y-%m-%d %H:%i')
               ,0,1) AS enabled
    FROM ycx_children_book_times bt
    LEFT JOIN ycx_children_book b
        ON b.doctor_id=#{doctorId} AND b.book_date=#{bookDate}
          AND DATE_FORMAT(b.book_begin_time,'%H:%i')=DATE_FORMAT(bt.book_begin_time,'%H:%i')
          AND b.`status`>=0
  </select>
  <select id="getMonthList" resultType="com.bmh.project.book.vo.MonthBookVo">
    SELECT book_date AS bookDate,COUNT(1) AS quantity FROM ycx_children_book
    WHERE org_id = #{orgId} AND status>=0
      AND is_book = 1
      AND book_date >= #{bookDateS} AND book_date &lt; #{bookDateE}
    AND book_date >= DATE_FORMAT(NOW(),'%Y-%m-%d')
      <if test="doctorId != null and doctorId > 0">
          AND doctor_id=#{doctorId}
      </if>
    GROUP BY book_date ORDER BY book_date ASC
  </select>
  <select id="getDayList" resultType="com.bmh.project.book.vo.BookingInfoVo">
    SELECT b.id,b.phone,b.name,b.logo,b.gender,b.birthday,b.children_id AS childrenId,
           b.book_date AS bookDate,b.book_begin_time AS bookBeginTime,
           b.book_end_time AS bookEndTime,b.month_age AS monthAge,
           b.doctor_id AS doctorId,
           b.status,
           b.pregnant_week AS pregnantWeek,
           u.name AS doctorName FROM ycx_children_book b
    LEFT JOIN sys_user u ON u.id=b.doctor_id
    WHERE b.org_id = #{orgId}
      AND b.is_book = 1
      AND  b.status>=0 AND b.book_date>= #{bookDateS} AND b.book_date &lt;= #{bookDateE}
      <if test="doctorId != null and doctorId > 0">
         AND b.doctor_id=#{doctorId}
      </if>
      <if test="exceptFinish !=null and exceptFinish ==1">
         AND b.status &lt; 2
      </if>
    ORDER BY b.status ASC,b.book_begin_time ASC
  </select>
    <select id="getDoctorEvaluatingCount" resultType="com.bmh.project.book.vo.DoctorEvaluatingCountVo">
      SELECT COUNT(`status`=0 OR NULL) AS evaluating0,
             COUNT(`status`=1 OR NULL) AS evaluating1,
             COUNT(`status`=2 OR NULL) AS evaluating2
      FROM ycx_children_book
      WHERE `status`>=0 AND doctor_id = #{doctorId}
    </select>
  <select id="getChildrenBookList" resultType="com.bmh.project.book.vo.BookingInfoVo">
    SELECT b.id,b.phone,b.name,b.logo,b.gender,b.birthday,b.children_id AS childrenId,
           b.book_date AS bookDate,b.book_begin_time AS bookBeginTime,
           b.book_end_time AS bookEndTime,b.month_age AS monthAge,
           b.doctor_id AS doctorId,
           b.status,
           b.pregnant_week AS pregnantWeek,
           u.name AS doctorName FROM ycx_children_book b
    LEFT JOIN sys_user u ON u.id=b.doctor_id
    WHERE b.status>=0 AND b.status &lt;2 AND b.doctor_id=#{doctorId}
    ORDER BY b.status DESC,b.book_begin_time ASC
  </select>
    <select id="getEvaluatingProjectList" resultType="com.bmh.project.book.vo.NearProjectVo">
        SELECT id AS bookId,create_time AS createTime,children_id AS childrenId,
               book_begin_time AS bookBeginTime
        FROM ycx_children_book b
        WHERE b.status>=0 AND b.status &lt;2 AND b.doctor_id=#{doctorId}
        ORDER BY (CASE
        WHEN b.status = 1 THEN 1
        WHEN b.status = 0 THEN 2
        WHEN b.status = 2 THEN 3
        ELSE 4 END) ASC, b.create_time DESC
    </select>
    <select id="getHistoryRecordList" resultType="com.bmh.project.book.vo.NearProjectVo">
        SELECT b.id AS bookId,b.create_time AS createTime,b.children_id AS childrenId,
        b.book_begin_time AS bookBeginTime
        FROM ycx_children_book b
        LEFT JOIN ycx_children c ON c.id=b.children_id
        LEFT JOIN ycx_children_evaluating e ON e.book_id = b.id
        LEFT JOIN ycx_children_evaluating_project ep ON ep.evaluating_id=e.id and ep.is_delete = 0
        WHERE b.status = 2 AND b.doctor_id=#{doctorId}
        AND ep.status=2
          <if test="childrenName != null and childrenName != ''">
              AND c.`name` LIKE CONCAT('%',#{childrenName},'%')
          </if>
        GROUP BY b.id
        HAVING COUNT(ep.id) > 0
        ORDER BY b.create_time DESC
    </select>

  <select id="supervisorGetHistoryRecordList" resultType="com.bmh.project.book.vo.NearProjectVo">
      SELECT b.id AS bookId,b.create_time AS createTime,b.children_id AS childrenId,b.doctor_id AS doctorId,
      su.name as doctorName, b.book_begin_time AS bookBeginTime
      FROM ycx_children_book b
      LEFT JOIN ycx_children c ON c.id=b.children_id
      LEFT JOIN ycx_children_evaluating e ON e.book_id = b.id
      LEFT JOIN ycx_children_evaluating_project ep ON ep.evaluating_id=e.id and ep.is_delete = 0
      LEFT JOIN sys_user su ON su.id = b.doctor_id
      WHERE b.status = 2 AND c.id = #{childrenId}
      AND ep.status=2
      GROUP BY b.id
      HAVING COUNT(ep.id) > 0
      ORDER BY b.create_time DESC
    </select>
</mapper>