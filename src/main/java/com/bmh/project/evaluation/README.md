评估流程

1.添加预约 ：/book/addBook 返回预约id

2.根据预约id获取预约信息 /book/getBookInfo

3.开始评估 /evaluating/startEvaluating 

```
传入预约id和评估项目id
返回大的评估Id`evaluatingId`以及具体评估项目的评估id`recordId`
```

4.创建测评记录/childrenRecord/createRecord 

```
也会返回一些数据，但是好像没什么用
```

5.每个评估回显使用 第三步的recordId

6.添加每条评估记录也是绑定 第三步的评估记录

7.最后保存评估时传入的也是 YcxChildrenRecord 对象中的id 也是第三步的recordId,导致ycx_children_record跟新数据时时混乱的

真实的修改评估状态须在每个评估自己实现的saveResult中修改

8.评估历史等接口查询的是 ycx_children_evaluating，ycx_children_evaluating_project这两个表以及每个评估自己的主表

结论：

```
ycx_children_record没什么用
```

