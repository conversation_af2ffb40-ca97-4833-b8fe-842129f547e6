<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ea.mapper.EaChildFlowSubNodeMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ea.model.EaChildFlowSubNode">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_flow_id" jdbcType="INTEGER" property="childFlowId" />
    <result column="child_flow_node_id" jdbcType="INTEGER" property="childFlowNodeId" />
    <result column="node_id" jdbcType="INTEGER" property="nodeId" />
    <result column="sub_node_id" jdbcType="INTEGER" property="subNodeId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="standard" jdbcType="VARCHAR" property="standard" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="full_name" jdbcType="VARCHAR" property="fullName" />
  </resultMap>


    <resultMap id="DetailWithRecordResultMap" type="com.bmh.project.ea.vo.EaChildFlowSubNodeVo">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="org_id" jdbcType="INTEGER" property="orgId" />
        <result column="child_flow_id" jdbcType="INTEGER" property="childFlowId" />
        <result column="child_flow_node_id" jdbcType="INTEGER" property="childFlowNodeId" />
        <result column="node_id" jdbcType="INTEGER" property="nodeId" />
        <result column="sub_node_id" jdbcType="INTEGER" property="subNodeId" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="standard" jdbcType="VARCHAR" property="standard" />
        <result column="status" jdbcType="INTEGER" property="status" />

        <collection property="recordList" ofType="com.bmh.project.ea.vo.EaCourseRecordListVo">
            <id column="record_id" property="id"/>
            <result column="course_id" property="courseId"/>
            <result column="target_done" property="targetDone"/>
            <result column="has_remark" property="hasRemark"/>

            <collection property="recordDetailList" ofType="com.bmh.project.ea.vo.EaCourseRecordDetailListVo">
                <id column="detail_id" property="id"/>
                <result column="problem_behavior_id" property="problemBehaviorId"/>
                <result column="problem_behavior" property="problemBehavior"/>
            </collection>

        </collection>
    </resultMap>
  <select id="getSubNodeWithRecordList" resultMap="DetailWithRecordResultMap">
      select eofsn.id,
             eofsn.org_id,
             eofsn.child_flow_id,
             eofsn.child_flow_node_id,
             eofsn.node_id,
             eofsn.sub_node_id,
             eofsn.name,
             eofsn.standard,
             eofsn.status,
             eocr.id as record_id,
             eocr.course_id,
             eocr.target_done,
             detail.id as detail_id,
             detail.problem_behavior_id,
             detail.problem_behavior,
             case
                 when eocr.id is null
                     then null
                 when eocr.id is not null and
                      (COALESCE(eocr.remark, eocr.problem_behavior, eocr.problem_reason,
                                eocr.problem_solution) is not null)
                     then 1
                 else 0
                 end as has_remark
      from ea_child_flow_sub_node eofsn
               left join ea_course_record eocr
                         on eocr.course_id = #{courseId} and eofsn.id = eocr.child_flow_sub_node_id and eocr.status = 1
               left join ea_course_record_detail detail
                         on detail.record_id = eocr.id and  detail.status = 1

      where eofsn.child_flow_node_id = #{childFlowNodeId}
        and eofsn.status = 1
      order by eofsn.order_num, eocr.create_time
  </select>
</mapper>