package com.bmh.project.supervisor.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.supervisor.model.SysSupervisor;
import com.bmh.project.supervisor.model.SysSupervisorScope;

import java.util.List;

/**
 * sys_supervisor_scope表对应的Service接口
 *
 * <AUTHOR>
 * @date 2023年07月24日 11:21:32
 */
public interface SysSupervisorScopeService extends BaseService<SysSupervisorScope> {

    /**
     * 根据监管老师ID获取监管机构列表
     * @param supervisorId 监管老师ID
     * @return
     */
    List<SysSupervisorScope> getListBySupervisorId(Integer supervisorId);

    /**
     * 根据监管机构id获得监管老师ID列表
     * @param orgId 机构ID
     * @return
     */
    List<SysSupervisorScope> getListByOrgId(Integer orgId);
}