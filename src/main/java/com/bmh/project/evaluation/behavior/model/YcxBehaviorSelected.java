package com.bmh.project.evaluation.behavior.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_behavior_selected")
public class YcxBehaviorSelected implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 问题项ID
     */
    @Column(name = "problem_id")
    private Integer problemId;

    /**
     * 选项(-1不能完成，0碰巧完成，1完成，2轻易完成，3熟练完成)
     */
    @Column(name = "answer")
    private String answer;

    @Column(name = "status")
    private Integer status;

    @Column(name = "create_time")
    private Date createTime;

    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}