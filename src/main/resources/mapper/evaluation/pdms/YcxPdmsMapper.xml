<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.pdms.mapper.YcxPdmsMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.pdms.model.YcxPdms">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="month" jdbcType="INTEGER" property="month" />
    <result column="operation" jdbcType="VARCHAR" property="operation" />
    <result column="step" jdbcType="VARCHAR" property="step" />
    <result column="imag" jdbcType="VARCHAR" property="imag" />
    <result column="stimulate" jdbcType="VARCHAR" property="stimulate" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="begin_month" jdbcType="INTEGER" property="beginMonth" />
    <result column="end_month" jdbcType="INTEGER" property="endMonth" />

    <collection property="options" ofType="com.bmh.project.evaluation.pdms.model.YcxPdmsOption" >
      <result column="pdms_id" jdbcType="INTEGER" property="pdmsId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="value" jdbcType="VARCHAR" property="value" />
      <result column="score" jdbcType="VARCHAR" property="score" />
    </collection>
  </resultMap>
    <select id="getPdms" resultMap="BaseResultMap">
      select pdms.*
    , op.pdms_id
    , op.name
    , op.value
    , op.score
    from ycx_pdms as pdms
    inner join ycx_pdms_option as op
    on op.pdms_id = pdms.id
    where pdms.type = #{type}
    and pdms.status = 1
    order by pdms.order_num asc,op.value asc, op.id asc
    </select>
</mapper>