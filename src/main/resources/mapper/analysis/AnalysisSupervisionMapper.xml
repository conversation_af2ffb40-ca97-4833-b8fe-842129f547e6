<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bmh.project.analysis.mapper.AnalysisSupervisionMapper">

    <select id="getAbaList" resultType="com.bmh.project.analysis.vo.AnalysisSupervisionVo">
        SELECT DATE(DATE_ADD( apph.create_time, INTERVAL 2 HOUR )) AS `date`,
               SUM(IF(type=0,1,0)) AS addCount ,
               SUM(IF(type=1,1,0)) AS changeCount ,
               SUM(IF(type=2,1,0)) AS passCount ,
               SUM(IF(type=3,1,0)) AS stopCount,
               COUNT(DISTINCT apph.plan_id) AS supervisionChildCount
        FROM
            aba_plan ap
                LEFT JOIN aba_plan_project_history apph ON apph.plan_id = ap.id
        WHERE ap.`status` = 1
          AND ap.org_id = #{orgId}
        <if test="queryStartDate != null">
            AND DATE(DATE_ADD( apph.create_time, INTERVAL 2 HOUR )) >= DATE ( #{queryStartDate})
        </if>
        <if test="queryEndDate != null">
            AND DATE(DATE_ADD( apph.create_time, INTERVAL 2 HOUR )) &lt;= DATE(#{queryEndDate})
        </if>
        GROUP BY `date`;
    </select>

    <select id="getAbnormalSupervisionList" resultType="com.bmh.project.analysis.vo.AnalysisSupervisionVo">
        SELECT
            abnormal_date AS `date`,
            COUNT(DISTINCT child_id) AS abnormalChildCount
        FROM
            `aba_abnormal_supervision`
        WHERE org_id = #{orgId}
        <if test="queryStartDate != null">
            AND DATE(abnormal_date) >= DATE ( #{queryStartDate})
        </if>
        <if test="queryEndDate != null">
            AND DATE(abnormal_date) &lt;= DATE(#{queryEndDate})
        </if>
        GROUP BY abnormal_date
    </select>
</mapper>