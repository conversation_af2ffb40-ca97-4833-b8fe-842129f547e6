<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ev.mapper.EvPepSynthesisDomainResultMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ev.model.EvPepSynthesisDomainResult">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="result_id" jdbcType="INTEGER" property="resultId" />
    <result column="synthesis_domain" jdbcType="VARCHAR" property="synthesisDomain" />
    <result column="dev_age" jdbcType="DECIMAL" property="devAge" />
    <result column="percentage_series" jdbcType="VARCHAR" property="percentageSeries" />
    <result column="adapt_level" jdbcType="VARCHAR" property="adaptLevel" />
    <result column="standard_score_count" jdbcType="INTEGER" property="standardScoreCount" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

    <select id="getSynthesisDomainResultByResultId" resultType="com.bmh.project.ev.vo.PepsynthesisDomainResultVo">
        select r.result_id as resultId,
               r.synthesis_domain as synthesisDomain,
               r.dev_age as devAge,
               r.percentage_series as percentageSeries,
               r.adapt_level as adaptLevel,
               r.standard_score_count as standardScoreCount
        from ev_pep_synthesis_domain_result r
        where r.is_delete = 0
          and r.result_id = #{resultId}
        ORDER BY
            case when r.synthesis_domain = '沟通（C）' then 1
                 when r.synthesis_domain = '体能（M）' then 2
                 when r.synthesis_domain = '行为（MB）' then 3
                 else 4 end
    </select>
</mapper>
