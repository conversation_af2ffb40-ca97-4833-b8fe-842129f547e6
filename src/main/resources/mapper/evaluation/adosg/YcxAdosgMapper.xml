<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.adosg.mapper.YcxAdosgMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.adosg.model.YcxAdosg">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="is_core" jdbcType="INTEGER" property="isCore" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />


    <collection property="options" ofType="com.bmh.project.evaluation.adosg.model.YcxAdosgOption">
      <result column="adosg_id" jdbcType="INTEGER" property="adosgId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="value" jdbcType="VARCHAR" property="value" />
    </collection>


  </resultMap>


<!--  List<YcxAdosg> getAdosgListByType(Integer type);-->
  <select id="getAdosgListByType" parameterType="Integer" resultMap="BaseResultMap" >
    select adosg.*
    , op.adosg_id
    , op.name
    , op.value
    from ycx_adosg as adosg
    inner join ycx_adosg_option as op
    on op.adosg_id = adosg.id
    where adosg.type = #{type}
    and adosg.status = 1
    order by adosg.order_num asc,op.value asc, op.id asc
  </select>

</mapper>