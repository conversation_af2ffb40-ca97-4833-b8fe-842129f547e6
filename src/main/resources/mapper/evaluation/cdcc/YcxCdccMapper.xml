<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.cdcc.mapper.YcxCdccMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.cdcc.model.YcxCdcc">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="month" jdbcType="DOUBLE" property="month" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="tips" jdbcType="VARCHAR" property="tips"/>
    <result column="grading" jdbcType="VARCHAR" property="grading"/>
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.cdcc.model.YcxCdccOption">
      <result column="cdcc_id" property="cdccId"/>
      <result column="name" property="name"/>
      <result column="value" property="value"/>
      <result column="score" property="score"/>
    </collection>
  </resultMap>

  <select id="getCdccListByType" resultMap="BaseResultMap">
    select cdcc.*
    , op.cdcc_id
    , op.name
    , op.value
    , op.score
    from ycx_cdcc as cdcc
    inner join ycx_cdcc_option as op
    on op.cdcc_id = cdcc.id
    where cdcc.type = #{type}
    and cdcc.status = 1
    order by cdcc.order_num asc, op.value asc, op.id asc
  </select>
</mapper>