<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ea.mapper.EaProblemSolutionMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ea.model.EaProblemSolution">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="node_id" jdbcType="INTEGER" property="nodeId" />
    <result column="behavior_id" jdbcType="INTEGER" property="behaviorId" />
    <result column="reason_id" jdbcType="INTEGER" property="reasonId" />
    <result column="solution" jdbcType="VARCHAR" property="solution" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>