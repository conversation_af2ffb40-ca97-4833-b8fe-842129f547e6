<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaPlanProjectGoalMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaPlanProjectGoal">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="org_id" jdbcType="INTEGER" property="orgId"/>
        <result column="child_id" jdbcType="INTEGER" property="childId"/>
        <result column="plan_id" jdbcType="INTEGER" property="planId"/>
        <result column="domain_id" jdbcType="INTEGER" property="domainId"/>
        <result column="plan_project_id" jdbcType="INTEGER" property="planProjectId"/>
        <result column="project_id" jdbcType="INTEGER" property="projectId"/>
        <result column="project_name" jdbcType="VARCHAR" property="projectName"/>
        <result column="short_goal_id" jdbcType="INTEGER" property="shortGoalId"/>
        <result column="short_goal_name" jdbcType="VARCHAR" property="shortGoalName"/>
        <result column="assist_method" jdbcType="VARCHAR" property="assistMethod"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="record_type" jdbcType="INTEGER" property="recordType"/>
        <result column="opt_type" jdbcType="INTEGER" property="optType"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
        <result column="remarks" jdbcType="VARCHAR" property="remarks"/>
        <result column="target" jdbcType="LONGVARCHAR" property="target"/>
    </resultMap>
    <select id="getChildrenCompleteCount" resultType="com.bmh.project.dashboard.vo.ChildRankVo">
        SELECT yc.`name` AS childName,
               appg.child_id AS childId,
               COUNT(appg.id)  AS shortGoalCount
        FROM aba_plan_project_goal appg
                 LEFT JOIN ycx_children yc ON appg.child_id = yc.id
        WHERE appg.update_time &gt; #{startTime}
          AND appg.update_time &lt; #{endTime}
          AND appg.org_id = #{orgId}
          AND appg.`status` = 2
        GROUP BY appg.child_id
    </select>
    <select id="selectShortGoalCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT (short_goal_id))
        FROM aba_plan_project_goal
        <where>
            <if test="orgId!=0">
                AND org_id = #{orgId}
            </if>
            <if test="0==orgId">
                AND org_id NOT IN (1,19,43,65)
            </if>
        </where>
    </select>
    <select id="getOrgChildrenCompleteCount" resultType="com.bmh.project.dashboard.vo.ChildRankVo">
        SELECT so.`short_name` AS childName,
               appg.org_id AS childId,
               COUNT(appg.id)  AS shortGoalCount
        FROM aba_plan_project_goal appg
            LEFT JOIN sys_org so ON appg.org_id = so.id
        WHERE appg.update_time &gt; #{startTime}
          AND appg.update_time &lt; #{endTime}
          AND appg.org_id NOT IN ( 1, 19, 43, 65 )
          AND appg.`status` = 2
        GROUP BY appg.org_id
    </select>
    <select id="getGoalListBy" resultType="com.bmh.project.aba.model.AbaPlanProjectGoal">
        select DISTINCT
            project_id AS projectId,
            short_goal_id AS shortGoalId,
            short_goal_name AS shortGoalName,
            status
        from aba_plan_project_goal
        where plan_id = #{planId}
        <if test="status!=null">
            and status = #{status}
        </if>
        order by project_id
    </select>


</mapper>
