<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.vb.mapper.YcxVbDomainMsMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.vb.model.YcxVbDomainMs">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="ms" jdbcType="INTEGER" property="ms" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>