package com.bmh.project.evaluation.ygtss.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;

@Data
@Table(name = "ycx_ygtss_selected")
public class YcxYgtssSelected extends YcxResult implements Serializable {
    /**
     * 主键Id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 评测记录Id
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 问题Id
     */
    @Column(name = "ygtss_id")
    private Integer ygtssId;

    /**
     * 选中项
     */
    @Column(name = "answer")
    private String answer;

    /**
     * 内容
     */
    @Column(name = "text")
    private String text;

    /**
     * 得分
     */
    @Column(name = "score")
    private Integer score;

    /**
     * 状态
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}