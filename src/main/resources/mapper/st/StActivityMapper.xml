<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.st.mapper.StActivityMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.st.model.StActivity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="level_id" jdbcType="INTEGER" property="levelId" />
    <result column="level_name" jdbcType="VARCHAR" property="levelName" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="scope" jdbcType="VARCHAR" property="scope" />
    <result column="teach_aid" jdbcType="VARCHAR" property="teachAid" />
    <result column="operate_video" jdbcType="VARCHAR" property="operateVideo" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
    <select id="getAllActivityList" resultType="com.bmh.project.st.vo.StPlanProjectVo">
        SELECT DISTINCT
            sa.id AS activityId,
            sa.level_name AS levelName,
            sa.domain_id AS domainId,
            sa.domain_name AS domainName,
            sa.project_id AS projectId,
            sa.project_name AS projectName,
            sa.`name` AS activityName
        FROM
            st_activity sa
        ORDER BY domain_id,project_id,id
    </select>
    <select id="getActivityDetailByTargetId" resultType="com.bmh.project.st.vo.StPlanProjectVo">
        SELECT
            sa.level_name AS levelName,
            sa.domain_name AS domainName,
            sa.project_name projectName,
            sa.id AS activityid,
            sa.`name` AS activityName,
            sat.`name` AS targetName
        FROM
            st_activity sa
                INNER JOIN st_activity_target sat ON sa.id = sat.activity_id
        WHERE
            sat.id = #{targetId}
        ORDER BY sa.id
    </select>
</mapper>
