package com.bmh.project.evaluation.conners.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.conners.mapper.YcxConnersPsqMapper;
import com.bmh.project.evaluation.conners.model.YcxConnersPsq;
import com.bmh.project.evaluation.conners.service.YcxConnersPsqService;
import com.bmh.project.evaluation.snap.model.YcxSnap;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * YcxConnersPsqService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月10日 14:43:46
 */
@Service
public class YcxConnersPsqServiceImpl extends BaseServiceImpl<YcxConnersPsq> implements YcxConnersPsqService {
    @Resource
    private YcxConnersPsqMapper ycxConnersPsqMapper;

    /**
     * 根据类型获取问题
     *
     * @param type 类型(1品行问题，2学习问题，3心身问题，4冲动-多动， 5焦虑，6其他)
     * @return
     */
    @Override
    public List<YcxConnersPsq> getListByType (Integer type) {
        return ycxConnersPsqMapper.getListByType (type);
    }
}