<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.user.mapper.YcxChildrenMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.user.model.YcxChildren">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="child_no" jdbcType="VARCHAR" property="childNo"/>
        <result column="id_card" jdbcType="VARCHAR" property="idCard"/>
        <result column="guardian" jdbcType="VARCHAR" property="guardian"/>
        <result column="nation" jdbcType="VARCHAR" property="nation"/>
        <result column="province" jdbcType="INTEGER" property="province"/>
        <result column="city" jdbcType="INTEGER" property="city"/>
        <result column="area" jdbcType="INTEGER" property="area"/>
        <result column="address" jdbcType="VARCHAR" property="address"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="logo" jdbcType="VARCHAR" property="logo"/>
        <result column="gender" jdbcType="INTEGER" property="gender"/>
        <result column="pregnant_week" jdbcType="VARCHAR" property="pregnantWeek"/>
        <result column="link_mobile" jdbcType="VARCHAR" property="linkMobile"/>
        <result column="birthday" jdbcType="TIMESTAMP" property="birthday"/>
        <result column="is_city" jdbcType="INTEGER" property="isCity"/>
        <result column="org_id" jdbcType="INTEGER" property="orgId"/>
        <result column="is_leave" jdbcType="INTEGER" property="isLeave"/>
        <result column="creator" jdbcType="INTEGER" property="creator"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    </resultMap>


    <!--  List<YcxChildren> getChildList(Map<String, Object> param);-->
    <select id="getChildList" parameterType="map" resultMap="BaseResultMap">
        select DISTINCT
        yc.id,
        yc.child_no,
        yc.id_card,
        yc.nation,
        yc.guardian,
        yc.province,
        yc.city,
        yc.area,
        yc.name,
        yc.logo,
        yc.gender,
        yc.link_mobile,
        yc.address,
        yc.birthday,
        yc.is_city,
        yc.pregnant_week,
        yc.create_name,
        yc.is_leave,
        CASE
            WHEN ycc.aba_status = 0 AND ycc.st_status = 0 AND ea_status = 0 THEN 0
            WHEN ycc.aba_status = 1 AND ycc.st_status = 1 AND ea_status = 1 THEN 1
            ELSE 2
        END AS isCourseCompleted,
        CASE
            WHEN ap.id IS NULL AND sp.id IS NULL AND ecf.id IS NULL THEN 0
            WHEN ycc.aba_status = 1 AND ycc.st_status = 1 AND ycc.ea_status = 1 THEN 0
            ELSE 1
        END AS isHaveCourseType
        from ycx_children yc
        inner join ycx_children_config ycc on yc.id = ycc.id
        left join aba_plan ap on yc.id = ap.child_id
        left join st_plan sp on yc.id = sp.child_id
        left join ea_child_flow ecf on yc.id = ecf.child_id
        where yc.org_id = #{orgId}
        <if test="name != null">
            and yc.name like concat('%', #{name}, '%')
        </if>
        <if test="id != null and id > 0">
            and yc.id = #{id}
        </if>
        <if test="childIds != null and childIds.size() > 0">
            and yc.id in
            <foreach collection="childIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by yc.id desc
    </select>

    <select id="getAbaChildList" resultType="com.bmh.project.user.vo.YcxAbaChildVo">
        select yc.id,
               yc.child_no            as childNo,
               yc.name,
               yc.logo,
               yc.gender,
               yc.birthday,
               yc.org_id              as orgId,
               ap.id                  as planId,
               ac.id                  as courseId,
               ac.teacher_id          as teacherId,
               yc.is_leave            as isLeave,
               ycc.aba_status AS isCourseCompleted,
               apr.id as prepId
        <if test="userType != null and userType == 2">
            ,max(dt.`is_continuous_pass`) AS `isContinuousPass`
            ,max(dt.`is_fail`)            AS `isFail`
            ,max(dt.`is_slow`)            AS `isSlow`
            ,max(dt.`is_step_pass`)            AS `isStepPass`
            ,case when IFNULL(max(dt.`is_continuous_pass`),0)+IFNULL(max(dt.`is_fail`),0)+IFNULL(max(dt.`is_slow`),0)+IFNULL(max(dt.`is_step_pass`),0)+IFNULL(max(his.`record_type`),0) >0 then 1 else 0 end  AS `needAdjust`
        </if>
        from ycx_children yc
            inner join ycx_children_config ycc on yc.id = ycc.id and ycc.status = 1
            left join aba_plan ap on yc.id = ap.child_id and ap.status = 1
            left join aba_course ac on yc.id = ac.child_id and ap.id = ac.plan_id and ac.status = 1
            left join aba_prep apr on  yc.id = apr.child_id and apr.teacher_id =  #{teacherId} and ap.id=apr.plan_id and apr.prep_date = date(NOW()) and apr.prep_rate >= 1.00
        <if test="userType != null and userType == 2">
            left join aba_plan_project app on ap.id = app.plan_id  and ap.child_id=app.child_id and app.status = 1
            left join aba_plan_project_goal gl on app.id = gl.plan_project_id and gl.record_type in (1,3) and gl.status = 1
            left join aba_plan_project_goal_data dt on gl.id = dt.plan_project_goal_id
            left join aba_plan_project_goal_history his on his.child_id = yc.id and his.record_type = 2 and is_confirm = 0
        </if>

        where yc.org_id = #{orgId}
        <if test="userType != null and userType == 1">
            and ycc.aba_status = 0
        </if>
        <if test="name != null and name != ''">
            and yc.name like concat('%', #{name}, '%')
        </if>
        <if test="havePlan != null">
            and if(ap.id is null, 0, 1) = #{havePlan}
        </if>
        <if test="childIds != null and childIds.size() > 0">
            and yc.id in
            <foreach collection="childIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        group by yc.id
    </select>

    <select id="checkChildExist" resultMap="BaseResultMap">
        select * from ycx_children
        where org_id = #{orgId}
        and name = #{name}
        and link_mobile = #{linkMobile}
    </select>

    <select id="getAbaPrepChildList" resultType="com.bmh.project.user.vo.YcxAbaPrepChildVo">
        select yc.id,
               yc.name,
               yc.logo,
               yc.gender,
               yc.birthday,
               yc.org_id   as orgId,
               ap.id       as planId,
               yc.is_leave as isLeave,
               apr.id      as prepId,
               apr.prep_rate as prepRate,
               ac.id          as courseId,
               ac.couseStatus  as couseStatus
        from ycx_children yc
            inner join ycx_children_config ycc on yc.id = ycc.id and ycc.status = 1
                 left join aba_plan ap on yc.id = ap.child_id and ap.status = 1
                 left join (select child_id, teacher_id,max(id) as id ,max(status) as couseStatus from  aba_course
                            where  DATE ( start_time ) = date(#{prepDate}) and teacher_id = #{teacherId}  group by child_id, teacher_id) ac  ON yc.id = ac.child_id
                 left join aba_prep apr on apr.child_id = yc.id
            and apr.teacher_id = #{teacherId}
            and apr.prep_date = date(#{prepDate})
        where yc.org_id = #{orgId}
          and ap.id is not null
          and ycc.aba_status = 0
        <if test="name != null and name != ''">
            and yc.name like concat('%', #{name}, '%')
        </if>
        <if test="childIds != null and childIds.size() > 0">
            and yc.id in
            <foreach collection="childIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        order by
        CASE
        WHEN apr.prep_rate = 1.00 THEN 1
        WHEN yc.is_leave = 1 THEN 2
        WHEN apr.prep_rate &lt; 1.00 THEN 3
        ELSE yc.id
        END ASC
    </select>

    <select id="getMccChildList" resultType="com.bmh.project.user.vo.YcxMccChildVo">
        select yc.id,
               yc.child_no                                   as childNo,
               yc.name,
               yc.logo,
               yc.gender,
               yc.birthday,
               yc.is_leave                                   as isLeave,
               if(mc.id is not null and mc.status = 2, 1, 0)     as todayHaveCourse,
               if(mc.id is not null and mc.status = 1, 1, 0) as isCourse,
               if(ap.id is null, 0, 1)                       as haveAbaPlan,
               tmp.maxTime                                   as lastCourseTime
        from ycx_children yc
                 left join aba_plan ap on yc.id = ap.child_id and ap.status = 1
                 left join mcc_course_person mcp
                           on mcp.type = 3 and yc.id = mcp.person_id and date(mcp.create_time) = current_date
                 left join mcc_course mc on mcp.course_id = mc.id and date(mc.start_time) = current_date
                 left join (select tmp.person_id, max(tmp.create_time) as maxTime
                            from mcc_course_person tmp
                            where tmp.type = 3
                            group by tmp.person_id) tmp on tmp.person_id = yc.id
        where yc.org_id = #{orgId}
        <if test="name != null and name != ''">
            and yc.name like concat('%', #{name}, '%')
        </if>
        group by yc.id
        <if test="userType != null and userType == 1">
            ORDER BY (CASE
            WHEN isCourse = 1 THEN 1
            WHEN todayHaveCourse = 0 THEN 2
            WHEN isLeave = 1 THEN 3
            WHEN todayHaveCourse = 1 THEN 4
            ELSE 5 END) ASC
        </if>
        <if test="userType != null and userType == 2">
            ORDER BY lastCourseTime desc
        </if>
    </select>

    <select id="getStChildList" resultType="com.bmh.project.user.vo.YcxStChildVo">
        select yc.id,
               yc.org_id               as orgId,
               yc.child_no             as childNo,
               yc.name,
               yc.logo,
               yc.gender,
               yc.birthday,
               yc.is_leave             as isLeave,
               ycc.st_status AS isCourseCompleted,
               if(sc.id is null, 0, 1) as isCourse,
               if(sp.id is null, 0, 1) as haveStPlan,
               sp.id                   as planId,
               sc.id                   as courseId,
               sc.teacher_id           as teacherId
        <if test="userType != null and userType == 2">
            ,if(max(spa.badge) > 0, 1, 0) AS needAdjust
        </if>
        from ycx_children yc
            inner join ycx_children_config ycc on yc.id = ycc.id and ycc.status = 1
            left join st_plan sp on yc.id = sp.child_id and sp.status = 1
            left join st_course sc on sc.child_id = yc.id and date(sc.start_time) = current_date and sc.status = 1
        <if test="userType != null and userType == 2">
            left join st_plan_activity spa on spa.child_id = yc.id and spa.status = 1
        </if>
        where yc.org_id = #{orgId}
        <if test="userType != null and userType == 1">
            and ycc.st_status = 0
        </if>
        <if test="name != null and name != ''">
            and yc.name like concat('%', #{name}, '%')
        </if>
        <if test="havePlan != null">
            and if(sp.id is null, 0, 1) = #{havePlan}
        </if>
        <if test="childIds != null and childIds.size() > 0">
            and yc.id in
            <foreach collection="childIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        group by yc.id
        order by CASE
                     WHEN isCourse = 1 THEN 1
                     WHEN haveStPlan = 1 THEN 2
                     ELSE yc.id
                     END ASC
    </select>

    <select id="getEvaluatingChildList" resultType="com.bmh.project.user.vo.YcxEvaluatingChildrenVo">
        select
        yc.id,
        yc.child_no as childNo,
        yc.name,
        yc.logo,
        yc.gender,
        yc.link_mobile as linkMobile,
        yc.address,
        yc.birthday,
        yc.is_city as isCity,
        yc.pregnant_week,
        yc.create_name as createName,
        yc.is_leave,
        CASE
        WHEN ycc.aba_status = 0 AND ycc.st_status = 0 AND ea_status = 0 THEN 0
        WHEN ycc.aba_status = 1 AND ycc.st_status = 1 AND ea_status = 1 THEN 1
        ELSE 2
        END AS isCourseCompleted,
        if(e2.id is not null and e2.submit_time is null , e2.record_id, null) as recordId,
        if(e2.id is not null and e2.submit_time is null , e2.book_id, null) as bookId
        from ycx_children yc
        inner join ycx_children_config ycc on yc.id = ycc.id and ycc.status = 1
        left join (
        SELECT
        e.id,
        e.children_id,
        e.book_id,
        ep.id AS record_id,
        ep.submit_time
        FROM
        ycx_children_evaluating e
        INNER JOIN (
                SELECT
                max( e.id ) AS id
                FROM
                ycx_children_evaluating e
                LEFT JOIN ycx_children_evaluating_project ep ON e.id = ep.evaluating_id AND ep.is_delete = 0
                WHERE
                e.doctor_id = #{sysUserId}
                AND ep.id IS NOT NULL GROUP BY e.children_id
                ) e1 ON e.id = e1.id
                LEFT JOIN ycx_children_evaluating_project ep ON e1.id = ep.evaluating_id
                AND ep.project_id = #{evaluatingProjectId}
                AND ep.is_delete = 0
                WHERE
                ep.id is not null
                AND e.org_id = #{orgId}
        ) e2 on e2.children_id = yc.id
        where yc.org_id = #{orgId}
        <if test="name != null">
            and yc.name like concat('%', #{name}, '%')
        </if>
        order by (CASE
        WHEN e2.id is not null and e2.submit_time is null THEN 1
        ELSE 2 END), e2.submit_time desc
    </select>

    <select id="getProgressCourseCount" resultType="java.lang.Integer">
        select count(1) from ycx_children yc
        left join aba_course ac on yc.id = ac.child_id and ac.status = 1
        left join st_course sc on yc.id = sc.child_id and sc.status = 1
        where yc.id = #{childrenId}
        and (ac.id is not null or sc.id is not null)
    </select>

    <select id="getEaChildList" resultType="com.bmh.project.user.vo.YcxEaChildVo">
        select yc.id,
        yc.child_no            as childNo,
        yc.name,
        yc.logo,
        yc.gender,
        yc.birthday,
        yc.org_id              as orgId,
        ec.id                  as courseId,
        ec.teacher_id          as teacherId,
        yc.is_leave            as isLeave,
        ycc.ea_status AS isCourseCompleted,
        IFNULL(cf.grade_type,0) as  gradeType,
        case when cf.id is null then 0 else 1 end as  isHaveFlow,
        IFNULL(cf.id,0) as childFlowId
        from ycx_children yc
        inner join ycx_children_config ycc on yc.id = ycc.id and ycc.status = 1
        left join ea_course ec on yc.id = ec.child_id and ec.status = 1
        left join ea_child_flow  cf  on  cf.org_id = yc.org_id and cf.child_id = yc.id and cf.status =1
        left join (SELECT org_id,child_id,max(create_time)  as create_time  from ea_child_flow where status =1
        GROUP BY org_id,child_id )  cfMax  on  cfMax.org_id = cf.org_id and cfMax.child_id = cf.id and cfMax.create_time =cf.create_time
        where yc.org_id = #{orgId}
        <if test="name != null and name != ''">
            and yc.name like concat('%', #{name}, '%')
        </if>
        <if test="isHaveFlow != null and isHaveFlow != ''">
            and case when cf.id is null then 0 else 1 end = #{isHaveFlow}
        </if>
        <if test="isHaveFlow == null or isHaveFlow == ''">
            and ycc.ea_status = 0
        </if>
        <if test="childIds != null and childIds.size() > 0">
            and yc.id in
            <foreach collection="childIds" item="id" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        group by yc.id
    </select>
    <select id="selectAllCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM ycx_children
    </select>
    <select id="selectChildrenCountByOrgId" resultType="java.lang.Integer">
        SELECT COUNT(id) FROM ycx_children WHERE org_id = #{orgId}
    </select>

    <select id="getTeacherChildList" resultType="com.bmh.project.user.vo.YcxTeacherChildVo">
        select yc.id,
        yc.child_no            as childNo,
        yc.name,
        yc.gender,
        yc.birthday,
        yc.org_id              as orgId,
        CASE
        WHEN ycc.aba_status = 0 AND ycc.st_status = 0 AND ea_status = 0 THEN 0
        WHEN ycc.aba_status = 1 AND ycc.st_status = 1 AND ea_status = 1 THEN 1
        ELSE 2
        END AS isCourseCompleted,
        GROUP_CONCAT(distinct u.id) as teacherIds,
        GROUP_CONCAT(distinct u.name) as teacherNames,
        max(t.create_time) as createTime
        from ycx_children yc
        left join ycx_children_config ycc on yc.id = ycc.id and ycc.status = 1
        left join ycx_children_teacher t on yc.id = t.child_id and t.status = 1 and date(t.expire_date)>=date(NOW())
        left join sys_user u on t.user_id = u.id and u.status = 1
        where yc.org_id = #{orgId}
        AND (ycc.aba_status = 0 OR ycc.st_status = 0 OR ycc.ea_status = 0)
        <if test="name != null and name != ''">
            and yc.name like concat('%', #{name}, '%')
        </if>
        <if test="gender != null">
            and yc.gender  = #{gender}
        </if>
        group by yc.id

    </select>

    <select id="getAttendList" resultType="com.bmh.project.user.vo.YcxTeacherChildVo">
        select yc.id,
        yc.child_no            as childNo,
        yc.name,
        yc.gender,
        yc.birthday,
        yc.org_id              as orgId,
        CASE
        WHEN ycc.aba_status = 0 AND ycc.st_status = 0 AND ea_status = 0 THEN 0
        WHEN ycc.aba_status = 1 AND ycc.st_status = 1 AND ea_status = 1 THEN 1
        ELSE 2
        END AS isCourseCompleted
        from ycx_children yc
        left join ycx_children_config ycc on yc.id = ycc.id and ycc.status = 1
        left join ycx_children_teacher t on yc.id = t.child_id and t.user_id =#{userId} and t.status = 1  and date(t.expire_date)>=date(NOW()) and  FIND_IN_SET(#{courseType},t.course_type)>0
        where yc.org_id = #{orgId}
        <if test="courseType == 1">
            AND ycc.aba_status = 0
        </if>
        <if test="courseType == 2">
            AND ycc.st_status = 0
        </if>
        <if test="courseType == 4">
            AND ycc.ea_status = 0
        </if>
        and t.id is null
        <if test="name != null and name != ''">
            and yc.name like concat('%', #{name}, '%')
        </if>
        <if test="gender != null">
            and yc.gender  = #{gender}
        </if>
        group by yc.id

    </select>
    <select id="selectAllEvChildren" resultType="com.bmh.project.user.model.YcxChildren">
        select
            yc.id,
            yc.name,
            yc.link_mobile
        from
            ycx_children yc
        left join ev_children ev on yc.id =ev.id and ev.status=1 and ev.is_delete=0 and ev.org_id = #{orgId}
        left join ycx_children_config ycc on yc.id = ycc.id
        where yc.org_id = #{orgId}
          and ycc.aba_status = 0
          and ev.id is null
    </select>


</mapper>
