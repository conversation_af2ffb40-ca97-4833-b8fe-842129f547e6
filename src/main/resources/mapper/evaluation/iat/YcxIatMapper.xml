<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.iat.mapper.YcxIatMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.iat.model.YcxIat">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />


    <collection property="options" ofType="com.bmh.project.evaluation.iat.model.YcxIatOption" >
      <result column="iat_id" jdbcType="INTEGER" property="iatId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="value" jdbcType="VARCHAR" property="value" />
      <result column="score" jdbcType="VARCHAR" property="score" />
    </collection>

  </resultMap>

  <!--获取问题列表-->
    <select id="getList" resultMap="BaseResultMap">
      select
        iat.*,
        op.iat_id,
        op.name,
        op.value,
        op.score
      from
        ycx_iat as iat
          left join
        ycx_iat_option as op
        on
          op.iat_id = iat.id
      order by
        iat.order_num asc,
        op.id asc
    </select>
</mapper>