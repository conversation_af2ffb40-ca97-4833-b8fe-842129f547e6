<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.user.mapper.YcxChildrenSurveyMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.user.model.YcxChildrenSurvey">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="residence" jdbcType="VARCHAR" property="residence" />
    <result column="primary_caregiver" jdbcType="VARCHAR" property="primaryCaregiver" />
    <result column="father_birth_age" jdbcType="INTEGER" property="fatherBirthAge" />
    <result column="father_residence" jdbcType="VARCHAR" property="fatherResidence" />
    <result column="father_profession" jdbcType="VARCHAR" property="fatherProfession" />
    <result column="mother_birth_age" jdbcType="INTEGER" property="motherBirthAge" />
    <result column="mother_profession" jdbcType="VARCHAR" property="motherProfession" />
    <result column="mother_residence" jdbcType="VARCHAR" property="motherResidence" />
    <result column="family_medical_history" jdbcType="VARCHAR" property="familyMedicalHistory" />
    <result column="birth_condition" jdbcType="VARCHAR" property="birthCondition" />
    <result column="growth_history" jdbcType="VARCHAR" property="growthHistory" />
    <result column="vision" jdbcType="VARCHAR" property="vision" />
    <result column="hearing" jdbcType="VARCHAR" property="hearing" />
    <result column="medical_diagnosis" jdbcType="VARCHAR" property="medicalDiagnosis" />
    <result column="comorbid_condition" jdbcType="VARCHAR" property="comorbidCondition" />
    <result column="serious_injury" jdbcType="VARCHAR" property="seriousInjury" />
    <result column="is_school" jdbcType="VARCHAR" property="isSchool" />
    <result column="reinforcement_food" jdbcType="VARCHAR" property="reinforcementFood" />
    <result column="reinforcement_toy" jdbcType="VARCHAR" property="reinforcementToy" />
    <result column="reinforcement_activity" jdbcType="VARCHAR" property="reinforcementActivity" />
    <result column="reinforcement_other" jdbcType="VARCHAR" property="reinforcementOther" />
    <result column="resistance_fear" jdbcType="VARCHAR" property="resistanceFear" />
    <result column="rehabilitation_history" jdbcType="VARCHAR" property="rehabilitationHistory" />
    <result column="rehabilitation_goal" jdbcType="VARCHAR" property="rehabilitationGoal" />
    <result column="source" jdbcType="INTEGER" property="source" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>