<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.lmt.mapper.YcxLmtProjectRecordMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.lmt.model.YcxLmtProjectRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="lmt_project_id" jdbcType="INTEGER" property="lmtProjectId" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="target_done" jdbcType="INTEGER" property="targetDone" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="calcPassCount" resultType="java.util.Map">
    select SUM(IF(target_done = 1, 1, 0)) AS passCount,
    SUM(IF(target_done = 0, 1, 0)) AS failCount
    from ycx_lmt_project_record
    where lmt_project_id = #{lmtProjectId}
    and status = 1
  </select>

  <select id="calcDomainResult" resultType="com.bmh.project.evaluation.lmt.model.YcxLmtDomain">
      select ylp.children_id                     as childrenId,
             ylp.record_id                       as recordId,
             ylp.domain_id                       as domainId,
             ylp.domain_name                     as domainName,
             sum(if(ylpr.id is not null and ylpr.target_done = 1, 1, 0)) as passCount,
             sum(if(ylpr.id is not null and ylpr.target_done = 0, 1, 0)) as failCount
      from ycx_lmt_project ylp
               left join ycx_lmt_project_record ylpr on ylpr.lmt_project_id = ylp.id and ylpr.status = 1
      where ylpr.record_id = #{recordId}
      group by ylp.domain_id
  </select>
</mapper>
