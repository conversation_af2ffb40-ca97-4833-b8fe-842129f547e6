<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaPlanProjectGoalDataMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaPlanProjectGoalData">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="plan_project_id" jdbcType="INTEGER" property="planProjectId" />
    <result column="plan_project_goal_id" jdbcType="INTEGER" property="planProjectGoalId" />
    <result column="short_goal_id" jdbcType="INTEGER" property="shortGoalId" />
    <result column="short_goal_name" jdbcType="VARCHAR" property="shortGoalName" />
    <result column="assist_method" jdbcType="VARCHAR" property="assistMethod" />
    <result column="record_type" jdbcType="INTEGER" property="recordType" />
    <result column="is_continuous_pass" jdbcType="INTEGER" property="isContinuousPass" />
    <result column="is_fail" jdbcType="INTEGER" property="isFail" />
    <result column="is_slow" jdbcType="INTEGER" property="isSlow" />
    <result column="is_step_pass" jdbcType="INTEGER" property="isStepPass" />
    <result column="try_item_total" jdbcType="INTEGER" property="tryItemTotal" />
    <result column="try_pass_count" jdbcType="INTEGER" property="tryPassCount" />
    <result column="calc_source" jdbcType="VARCHAR" property="calcSource" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="target_result" jdbcType="LONGVARCHAR" property="targetResult" />
  </resultMap>
</mapper>