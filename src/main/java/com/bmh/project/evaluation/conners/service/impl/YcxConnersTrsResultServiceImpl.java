package com.bmh.project.evaluation.conners.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.conners.mapper.YcxConnersTrsResultMapper;
import com.bmh.project.evaluation.conners.model.YcxConnersTrsResult;
import com.bmh.project.evaluation.conners.model.YcxConnersTrsSelected;
import com.bmh.project.evaluation.conners.service.YcxConnersTrsResultService;
import com.bmh.project.evaluation.conners.service.YcxConnersTrsSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxConnersTrsResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月10日 14:43:46
 */
@Service("22")
public class YcxConnersTrsResultServiceImpl extends BaseServiceImpl<YcxConnersTrsResult> implements YcxConnersTrsResultService, YcxResultService<YcxConnersTrsResult> {
    @Resource
    private YcxConnersTrsResultMapper ycxConnersTrsResultMapper;
    @Resource
    private YcxConnersTrsSelectedService trsSelectedService;

    /**
     * 保存结果
     *
     * @param childrenRecord
     */
    @Override
    public void saveResult (YcxChildrenRecord childrenRecord) {
        YcxConnersTrsResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxConnersTrsResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge());
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date ());
        ycxConnersTrsResultMapper.insert(result);

        List<YcxConnersTrsSelected> selecteds = result.getSelecteds ();
        trsSelectedService.saveSelecteds(selecteds, result.getRecordId());
    }

    /**
     * 获取评测结果
     *
     * @param recordId 记录ID
     * @return
     */
    @Override
    public YcxConnersTrsResult getResult (Integer recordId) {
        Example example = new Example(YcxConnersTrsResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxConnersTrsResult> list = ycxConnersTrsResultMapper.selectByExample(example);
        return CollectionUtil.isNotEmpty (list)?list.get(0):null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxConnersTrsResult result = new YcxConnersTrsResult ();
        result.setRecom (recom);
        Example example = new Example (YcxConnersTrsResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxConnersTrsResultMapper.updateByExampleSelective (result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}