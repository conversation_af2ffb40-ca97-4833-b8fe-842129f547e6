<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.pdms.mapper.YcxPdmsDictbMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.pdms.model.YcxPdmsDictb">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="sum" jdbcType="INTEGER" property="sum" />
    <result column="gmq" jdbcType="INTEGER" property="gmq" />
    <result column="fmq" jdbcType="INTEGER" property="fmq" />
    <result column="tmq" jdbcType="INTEGER" property="tmq" />
  </resultMap>
    <select id="getTmq" resultType="java.lang.Integer">
      select dict.tmq
	  from ycx_pdms_dict_b
	  as dict
	  where dict.sum = #{sum}
    </select>
</mapper>