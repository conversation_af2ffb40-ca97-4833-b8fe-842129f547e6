server.port=6021

#-------------------LOG---------------------------------
debug=true
logging.level.com.ycx=trace
logging.level.org.springframework.web=DEBUG
logging.config=classpath:logback.xml
logging.level.org.springframework.boot.autoconfigure=ERROR
logging.level.me.chanjar.weixin=DEBUG
#-------------------LOG---------------------------------

#-------------------Mysql---------------------------------
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=**************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=baomahe123
#spring.datasource.url=*************************************************************************************************************************************
#spring.datasource.username=root
#spring.datasource.password=ECNHrEmE6dEBAOMAHe!@#
#-------------------Mysql---------------------------------

#spring.data.mongodb.uri=mongodb://127.0.0.1:27017/ycx

#-------------------Redis---------------------------------
spring.redis.host=*************
spring.redis.port=6379
spring.redis.timeout=10000
#-------------------Reids---------------------------------

#-------------------Lettuce---------------------------------
spring.redis.database=1
spring.redis.lettuce.pool.max-idle=8
spring.redis.lettuce.pool.min-idle=1
spring.redis.lettuce.pool.max-active=20
spring.redis.lettuce.pool.max-wait=-1ms
#-------------------Lettuce---------------------------------

#-------------------Druid---------------------------------
# 初始连接数
spring.datasource.druid.initialSize=1
# 最小连接池数量
spring.datasource.druid.minIdle=1
# 最大连接池数量
spring.datasource.druid.maxActive=10
# 配置获取连接等待超时的时间
spring.datasource.druid.maxWait=60000
# 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
spring.datasource.druid.timeBetweenEvictionRunsMillis=60000
# 配置一个连接在池中最小生存的时间，单位是毫秒
spring.datasource.druid.minEvictableIdleTimeMillis=300000
# 配置一个连接在池中最大生存的时间，单位是毫秒（需小于数据库配置wait_timeout）
spring.datasource.druid.maxEvictableIdleTimeMillis=540000
#-------------------Druid---------------------------------


#-------------------ERP---------------------------------
erp.server.url=http://localhost:8076
erp.server.token=6Y0rSVh9l8V9F44s995D
#-------------------ERP---------------------------------

#-----------------AI-API------------------------------
deepseek.base-url=https://ark.cn-beijing.volces.com/api/v3
#R1
deepseek.model=ep-20250218095645-tvpt4
#V3
deepseek.model.v3=ep-20250218095658-hs7f8
deepseek.token=16384
deepseek.token.v3=12288
deepseek.temperature=0.6
deepseek.api-key=e47b1f4c-86ec-4c36-9f6c-5e8589b0444e
deepseek.connect-timeout= 600
deepseek.read-timeout=600
deepseek.call-timeout=600
#-----------------AI-API------------------------------
