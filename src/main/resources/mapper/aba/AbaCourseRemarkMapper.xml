<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaCourseRemarkMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaCourseRemark">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="teacher_id" jdbcType="INTEGER" property="teacherId" />
    <result column="is_ontime" jdbcType="INTEGER" property="isOntime" />
    <result column="nontime_reason" jdbcType="VARCHAR" property="nontimeReason" />
    <result column="child_match" jdbcType="INTEGER" property="childMatch" />
    <result column="is_complete" jdbcType="INTEGER" property="isComplete" />
    <result column="uncomplete_reason" jdbcType="VARCHAR" property="uncompleteReason" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="video_url" jdbcType="VARCHAR" property="videoUrl" />
    <result column="supervisor_id" jdbcType="INTEGER" property="supervisorId" />
    <result column="supervisor_evaluation" jdbcType="VARCHAR" property="supervisorEvaluation" />
    <result column="evaluation_time" jdbcType="TIMESTAMP" property="evaluationTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="is_read" jdbcType="INTEGER" property="isRead" />
  </resultMap>
</mapper>