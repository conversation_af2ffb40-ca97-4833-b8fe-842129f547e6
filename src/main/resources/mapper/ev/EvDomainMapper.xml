<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ev.mapper.EvDomainMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ev.model.EvDomain">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="domain" jdbcType="VARCHAR" property="domain" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="vb_type" jdbcType="INTEGER" property="vbType" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>

    <select id="selectByType" resultType="com.bmh.project.ev.model.EvDomain">
    SELECT id,domain,vb_type as vbType FROM ev_domain WHERE `status` = 1 AND is_delete = 0 AND type = #{type}
    </select>

    <select id="selectByVbType" resultType="com.bmh.project.ev.model.EvDomain">
        SELECT id,domain,vb_type as vbType FROM ev_domain WHERE `status` = 1 AND is_delete = 0 AND type = #{type} AND vb_type = #{vbType}
    </select>

</mapper>
