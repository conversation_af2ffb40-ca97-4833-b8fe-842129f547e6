<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.st.mapper.StCourseActivityMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.st.model.StCourseActivity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="teacher_id" jdbcType="INTEGER" property="teacherId" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="plan_activity_id" jdbcType="INTEGER" property="planActivityId" />
    <result column="level_id" jdbcType="INTEGER" property="levelId" />
    <result column="level_name" jdbcType="VARCHAR" property="levelName" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="activity_id" jdbcType="INTEGER" property="activityId" />
    <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <insert id="insertBatchByCourse">
      insert into st_course_activity (org_id, child_id, teacher_id, course_id, plan_activity_id, level_id, level_name,
                                      domain_id, domain_name, project_id, project_name, activity_id, activity_name,
                                      create_user, create_time, update_time, update_user)
      select #{course.orgId}      as org_id,
             #{course.childId}    as child_id,
             #{course.teacherId}  as teacher_id,
             #{course.id}         as course_id,
             id                   as plan_activity_id,
             level_id,
             level_name,
             domain_id,
             domain_name,
             project_id,
             project_name,
             activity_id,
             activity_name,
             #{course.createUser} as create_user,
             now()                as create_time,
             now()                as update_time,
             #{course.updateUser} as update_user
      from st_plan_activity
      where plan_id = #{course.planId}
      and `status` = 1
  </insert>

    <select id="getDomainListByCourseId" resultType="com.bmh.project.st.vo.StCourseDomainVo">
        select course_id    as courseId,
        level_id   as levelId,
        level_name as levelName,
        domain_id  as domainId,
        domain_name as domainName
        from st_course_activity
        where course_id = #{courseId}
        group by domain_id
        order by level_id, domain_id
    </select>
    <select id="getProjectCount" resultType="java.lang.Integer">
        SELECT COUNT(project_id)
        FROM st_course_activity
        WHERE org_id = #{orgId}
          AND create_time &gt; #{startTime}
          AND create_time &lt; #{endTime}
    </select>
    <select id="getActivityCount" resultType="java.lang.Integer">
        SELECT COUNT(sca.activity_id)
        FROM st_course_activity sca
        LEFT JOIN st_course sc ON sca.course_id = sc.id
        WHERE sc.`status` = 2
          AND sca.create_time &gt; #{startTime}
          AND sca.create_time &lt; #{endTime}
          <if test="orgId!=0">
            AND sca.org_id = #{orgId}
          </if>
        <if test="0==orgId">
            AND sca.org_id NOT IN (1,19,43,65)
        </if>
    </select>

</mapper>
