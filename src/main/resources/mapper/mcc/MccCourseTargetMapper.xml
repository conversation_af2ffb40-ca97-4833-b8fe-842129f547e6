<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.mcc.mapper.MccCourseTargetMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.mcc.model.MccCourseTarget">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="org_id" jdbcType="INTEGER" property="orgId"/>
        <result column="course_id" jdbcType="INTEGER" property="courseId"/>
        <result column="child_id" jdbcType="INTEGER" property="childId"/>
        <result column="child_name" jdbcType="VARCHAR" property="childName"/>
        <result column="target_id" jdbcType="INTEGER" property="targetId"/>
        <result column="target_name" jdbcType="VARCHAR" property="targetName"/>
        <result column="segment" jdbcType="VARCHAR" property="segment"/>
        <result column="pass_count" jdbcType="INTEGER" property="passCount"/>
        <result column="fail_count" jdbcType="INTEGER" property="failCount"/>
        <result column="pass_rate" jdbcType="DOUBLE" property="passRate"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    </resultMap>

    <select id="getRecent14Data" resultType="com.bmh.project.mcc.vo.MccCourseTargetStatisticsVo">
        select mct.id,
               mct.course_id   as courseId,
               date(mc.start_time) as courseDate,
               mct.target_id   as targetId,
               mct.target_name as targetName,
               mct.pass_rate   as passRate
        from mcc_course_target mct
                 left join mcc_course mc on mct.course_id = mc.id
        where mct.child_id = #{childId}
          and mc.status = 2
          and date(mc.start_time) >= DATE_SUB(CURDATE(), INTERVAL 14 DAY)
        order by mct.target_id asc, courseDate asc
    </select>

    <select id="getCourseTargetVoList" resultType="com.bmh.project.mcc.vo.MccCourseTargetVo">
        select mct.id,
               mct.org_id      as orgId,
               mct.course_id   as courseId,
               mct.child_id    as childId,
               mct.child_name  as childName,
               mct.target_id   as targetId,
               mct.target_name as targetName,
               mct.segment,
               mct.pass_count  as passCount,
               mct.fail_count  as failCount,
               mct.pass_rate   as passRate,
               mct.remark,
               yc.logo         as childLogo
        from mcc_course_target mct
                 left join ycx_children yc on mct.org_id = yc.org_id and mct.child_id = yc.id
        where mct.course_id = #{courseId}
        <if test="childId != null">
            and mct.child_id = #{childId}
        </if>
        <if test="targetId != null">
            and mct.target_id = #{targetId}
        </if>
        order by mct.target_id asc, mct.child_id asc
    </select>
</mapper>
