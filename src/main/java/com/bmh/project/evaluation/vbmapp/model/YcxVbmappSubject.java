package com.bmh.project.evaluation.vbmapp.model;

import java.io.Serializable;
import java.util.List;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_vbmapp_subject")
public class YcxVbmappSubject implements Serializable {
    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 领域Id
     */
    @Column(name = "realm_id")
    private Integer realmId;

    /**
     * 名称
     */
    @Column(name = "title")
    private String title;

    /**
     * 排序号
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 标签
     */
    @Column(name = "label")
    private String label;

    /**
     * 限时分钟
     */
    @Column(name = "minute")
    private Integer minute;

    /**
     * 题目选项列表
     */
    @Transient
    private List<YcxVbmappSubjectOption> subjectOptions;

    /**
     * 题目指南
     */
    @Transient
    private List<YcxVbmappSubjectBook> subjectBooks;

    private static final long serialVersionUID = 1L;
}