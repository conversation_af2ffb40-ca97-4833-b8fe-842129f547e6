<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.nbna.mapper.YcxNbnaMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.nbna.model.YcxNbna">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.nbna.model.YcxNbnaOption">
      <result column="nbna_id" property="nbnaId"/>
      <result column="name" property="name"/>
      <result column="value" property="value"/>
      <result column="score" property="score"/>
    </collection>
  </resultMap>

  <select id="getNbnaListByType" resultMap="BaseResultMap">
    select nbna.*
    , op.nbna_id
    , op.name
    , op.value
    , op.score
    from ycx_nbna as nbna
    inner join ycx_nbna_option as op
    on op.nbna_id = nbna.id
    where nbna.type = #{type}
    and nbna.status = 1
    order by nbna.order_num asc, op.id asc
  </select>
</mapper>