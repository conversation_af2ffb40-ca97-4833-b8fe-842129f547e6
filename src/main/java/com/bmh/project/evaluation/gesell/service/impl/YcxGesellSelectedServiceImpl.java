package com.bmh.project.evaluation.gesell.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.gesell.mapper.YcxGesellSelectedMapper;
import com.bmh.project.evaluation.gesell.model.YcxGesellSelected;
import javax.annotation.Resource;

import com.bmh.project.evaluation.gesell.service.YcxGesellSelectedService;
import org.springframework.stereotype.Service;
import java.util.Date;
import java.util.List;

/**
 * YcxGesellOptionsService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月11日 11:38:14
 */
@Service
public class YcxGesellSelectedServiceImpl extends BaseServiceImpl<YcxGesellSelected> implements YcxGesellSelectedService {
    @Resource
    private YcxGesellSelectedMapper ycxGesellOptionsMapper;


    @Override
    public void saveSelecteds(List<YcxGesellSelected> gesellSelecteds, Integer recordId) {
        gesellSelecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        this.insertList(gesellSelecteds);
    }


}