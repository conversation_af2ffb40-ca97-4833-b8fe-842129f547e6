package com.bmh.project.evaluation.behavior.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.behavior.mapper.YcxBehaviorResultMapper;
import com.bmh.project.evaluation.behavior.model.YcxBehaviorResult;
import com.bmh.project.evaluation.behavior.model.YcxBehaviorSelected;
import com.bmh.project.evaluation.behavior.service.YcxBehaviorResultService;
import com.bmh.project.evaluation.behavior.service.YcxBehaviorSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxBehaviorResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月15日 12:21:59
 */
@Service("6")
public class YcxBehaviorResultServiceImpl extends BaseServiceImpl<YcxBehaviorResult> implements YcxBehaviorResultService, YcxResultService<YcxBehaviorResult> {
    @Resource
    private YcxBehaviorResultMapper ycxBehaviorResultMapper;
    @Resource
    private YcxBehaviorSelectedService behaviorSelectedService;

    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {

        YcxBehaviorResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxBehaviorResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge());
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date());
        this.insert(result);

        List<YcxBehaviorSelected> selecteds = result.getBehaviorSelecteds();
        behaviorSelectedService.saveSelecteds(selecteds, result.getRecordId());
    }


    @Override
    public YcxBehaviorResult getResult(Integer recordId) {
        Example example = new Example(YcxBehaviorResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxBehaviorResult> list = this.selectByExample(example);
        if(list!=null&&list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxBehaviorResult result = new YcxBehaviorResult ();
        result.setRecom (recom);
        Example example = new Example (YcxBehaviorResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxBehaviorResultMapper.updateByExampleSelective (result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }


}