<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.user.mapper.SysMessageMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.user.model.SysMessage">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="sender_id" jdbcType="INTEGER" property="senderId" />
    <result column="sender" jdbcType="VARCHAR" property="sender" />
    <result column="sender_type" jdbcType="INTEGER" property="senderType" />
    <result column="recipient_id" jdbcType="INTEGER" property="recipientId" />
    <result column="recipient" jdbcType="VARCHAR" property="recipient" />
    <result column="recipient_type" jdbcType="INTEGER" property="recipientType" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="is_read" jdbcType="INTEGER" property="isRead" />
    <result column="extra_param" jdbcType="LONGVARCHAR" property="extraParam" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="getPlanChangeMsgList" resultMap="BaseResultMap">
      select *
      from sys_message
      where recipient_id = #{recipientId}
        and type = #{planType}
        and recipient_type = #{recipientType}
        and extra_param ->> '$.planId' = #{planId}
        and is_read = 0
  </select>
</mapper>