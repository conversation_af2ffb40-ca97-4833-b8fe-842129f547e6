package com.bmh.project.supervisor.model;

import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;

@Data
@Table(name = "sys_supervisor_scope")
public class SysSupervisorScope implements Serializable {
    /**
     * 督导ID
     */
    @Column(name = "supervisor_id")
    private Integer supervisorId;

    /**
     * 机构ID
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 机构名称
     */
    @Transient
    private String orgName;

    /**
     * 机构简称
     */
    @Transient
    private String orgShortName;

    /**
     * 机构ERP ID
     */
    @Transient
    private Integer erpOrgId;

    /**
     * 督导姓名 name
     */
    @Transient
    private String supervisorName;


    private static final long serialVersionUID = 1L;
}