package com.bmh.project.evaluation.ruttert.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.ruttert.mapper.YcxRuttertMapper;
import com.bmh.project.evaluation.ruttert.model.YcxRuttert;
import com.bmh.project.evaluation.ruttert.service.YcxRuttertService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxRuttertService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月17日 09:39:00
 */
@Service
public class YcxRuttertServiceImpl extends BaseServiceImpl<YcxRuttert> implements YcxRuttertService {
    @Resource
    private YcxRuttertMapper ycxRuttertMapper;

    /**
     * 查询所有问题(老师问卷)
     * @return
     */
    @Override
    public List<YcxRuttert> getRutter() {
        return ycxRuttertMapper.getRutter();
    }
}