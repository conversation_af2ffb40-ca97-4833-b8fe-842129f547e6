<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaPrepProjectMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaPrepProject">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="prep_id" jdbcType="INTEGER" property="prepId" />
    <result column="prep_time" jdbcType="TIMESTAMP" property="prepTime" />
    <result column="prep_status" jdbcType="INTEGER" property="prepStatus" />
    <result column="plan_project_id" jdbcType="INTEGER" property="planProjectId" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_level" jdbcType="INTEGER" property="projectLevel" />
    <result column="long_goal" jdbcType="VARCHAR" property="longGoal" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="calcPrepRate" resultType="java.util.Map">
      select IFNULL(SUM(IF(prep_status = 1, 1, 0)), 0) AS passCount,
             count(id)                      AS totalCount
      from aba_prep_project
      where prep_id = #{prepId}
  </select>
</mapper>