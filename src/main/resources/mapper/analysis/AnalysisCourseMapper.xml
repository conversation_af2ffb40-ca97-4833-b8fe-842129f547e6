<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.analysis.mapper.AnalysisCourseMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.analysis.model.AnalysisCourse">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="course_type" jdbcType="INTEGER" property="courseType" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="org_short_name" jdbcType="VARCHAR" property="orgShortName" />
    <result column="teacher_id" jdbcType="INTEGER" property="teacherId" />
    <result column="teacher_name" jdbcType="VARCHAR" property="teacherName" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="child_ids" jdbcType="VARCHAR" property="childIds" />
    <result column="child_names" jdbcType="VARCHAR" property="childNames" />
    <result column="amount" jdbcType="NUMERIC" property="amount" />
  </resultMap>

  <select id="getOrgList" resultType="com.bmh.project.analysis.vo.AnalysisCourseOrgVo">
    SELECT
      org_id AS orgId,
      org_name AS orgName,
      org_short_name AS orgShortName,
      COUNT(course_id) AS totalCount,
      SUM(IF(course_type = 1 ,1,0)) AS abaCount,
      SUM(IF(course_type = 2 ,1,0)) AS stCount,
      SUM(IF(course_type = 3 ,1,0)) AS mccCount
    FROM
      view_analysis_course
    <where>
      <if test="orgId != null">
        AND org_id = #{orgId}
      </if>
      <choose>
        <when test="precondition != null and precondition == 1">
          AND DATE(end_time) = CURRENT_DATE
        </when>
        <when test="precondition  != null and precondition == 2">
          AND DATE(end_time) = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)
        </when>
        <when test="precondition  != null and precondition == 3">
                AND YEARWEEK(end_time, 1) = YEARWEEK(NOW(), 1)
        </when>
        <when test="precondition  != null and precondition == 4">
          AND DATE(end_time) >= DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY)
        </when>
      </choose>
      <if test="queryStartDate != null">
        AND DATE(end_time) >= DATE(#{queryStartDate})
      </if>
      <if test="queryEndDate != null">
        AND  DATE(end_time) &lt;= DATE(#{queryEndDate})
      </if>
    </where>
    GROUP BY org_id
    ORDER BY totalCount DESC
  </select>

  <select id="getTeacherList" resultType="com.bmh.project.analysis.vo.AnalysisCourseTeacherVo">
      SELECT
      su.org_id AS orgId,
      vac.org_short_name AS orgShortName,
      su.id AS teacherId,
      su.`name` AS teacherName,
      COUNT(vac.course_id) AS totalCount,
      SUM(IF(vac.course_type = 1 ,1,0)) AS abaCount,
      SUM(IF(vac.course_type = 2 ,1,0)) AS stCount,
      SUM(IF(vac.course_type = 3 ,1,0)) AS mccCount
      FROM sys_user su
      LEFT JOIN view_analysis_course vac ON su.id = vac.teacher_id
      WHERE su.`status` = 1
      AND su.role != '督导'
      <if test="orgId != null">
          AND su.org_id = #{orgId}
      </if>
      <choose>
          <when test="precondition != null and precondition == 1">
              AND DATE(vac.end_time) = CURRENT_DATE
          </when>
          <when test="precondition  != null and precondition == 2">
              AND DATE(vac.end_time) = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)
          </when>
          <when test="precondition  != null and precondition == 3">
              AND YEARWEEK(vac.end_time, 1) = YEARWEEK(NOW(), 1)
          </when>
          <when test="precondition  != null and precondition == 4">
              AND DATE(vac.end_time) >= DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY)
          </when>
      </choose>
      <if test="queryStartDate != null">
          AND DATE(vac.end_time) >= DATE(#{queryStartDate})
      </if>
      <if test="queryEndDate != null">
          AND  DATE(vac.end_time) &lt;= DATE(#{queryEndDate})
      </if>
      GROUP BY su.id
      ORDER BY totalCount DESC
  </select>

  <select id="getCourseTypeList" resultType="com.bmh.project.analysis.vo.AnalysisCourseTypeVo">
      SELECT course_type      AS courseType,
             COUNT(course_id) AS totalCount
      FROM view_analysis_course
      <where>
          <if test="orgId != null">
              AND org_id = #{orgId}
          </if>
          <choose>
              <when test="precondition != null and precondition == 1">
                  AND DATE(end_time) = CURRENT_DATE
              </when>
              <when test="precondition != null and precondition == 2">
                  AND DATE(end_time) = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)
              </when>
              <when test="precondition != null and precondition == 3">
                  AND YEARWEEK(end_time, 1) = YEARWEEK(NOW(), 1)
              </when>
              <when test="precondition != null and precondition == 4">
                  AND DATE(end_time) >= DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY)
              </when>
          </choose>
          <if test="queryStartDate != null">
              AND DATE(end_time) >= DATE(#{queryStartDate})
          </if>
          <if test="queryEndDate != null">
              AND DATE(end_time) &lt;= DATE(#{queryEndDate})
          </if>
      </where>
      GROUP BY course_type
      ORDER BY totalCount DESC
  </select>

  <select id="getCourseList" resultMap="BaseResultMap">
      SELECT vac.*
      FROM (
      <if test="courseType== null or (courseType != null and courseType == 1)">
          -- aba_course 子查询
          SELECT 1                   AS `course_type`,
                 `ac`.`id`           AS `course_id`,
                 `ac`.`org_id`       AS `org_id`,
                 `so`.`name`         AS `org_name`,
                 `so`.`short_name`   AS `org_short_name`,
                 `ac`.`teacher_id`   AS `teacher_id`,
                 `ac`.`teacher_name` AS `teacher_name`,
                 `ac`.`start_time`   AS `start_time`,
                 `ac`.`end_time`     AS `end_time`,
                 `ac`.`child_id`     AS `child_ids`,
                  yc.`name`          AS `child_names`,
                  bill.`amount`      AS `amount`
          FROM `aba_course` `ac`
                   LEFT JOIN `sys_org` `so` ON `ac`.`org_id` = `so`.`id`
                   LEFT JOIN `ycx_children` yc ON `ac`.`child_id` = yc.`id`
                   LEFT JOIN `sys_org_bill` bill ON `ac`.`id` = bill.`biz_id` and bill.second_biz_type=1
          WHERE `ac`.`org_id` = #{orgId}
            AND `ac`.`status` = 2
          <if test="teacherId != null">
              AND `ac`.`teacher_id` = #{teacherId}
          </if>
          <if test="childId != null">
              AND `ac`.`child_id` = #{childId}
          </if>
          <if test="queryStartDate != null">
              AND DATE(`ac`.`end_time`) >= DATE(#{queryStartDate})
          </if>
          <if test="queryEndDate != null">
              AND DATE(`ac`.`end_time`) &lt;= DATE(#{queryEndDate})
          </if>
      </if>
      <if test="courseType == null">
          UNION ALL
      </if>
      <if test="courseType== null or (courseType != null and courseType == 2)">
          -- st_course 子查询
          SELECT 2                   AS `course_type`,
                 `sc`.`id`           AS `course_id`,
                 `sc`.`org_id`       AS `org_id`,
                 `so`.`name`         AS `org_name`,
                 `so`.`short_name`   AS `org_short_name`,
                 `sc`.`teacher_id`   AS `teacher_id`,
                 `sc`.`teacher_name` AS `teacher_name`,
                 `sc`.`start_time`   AS `start_time`,
                 `sc`.`end_time`     AS `end_time`,
                 `sc`.`child_id`     AS `child_ids`,
                  yc.`name`          AS `child_names`,
                  bill.`amount`      AS `amount`
          FROM `st_course` `sc`
                   LEFT JOIN `sys_org` `so` ON `sc`.`org_id` = `so`.`id`
                   LEFT JOIN `ycx_children` yc ON `sc`.`child_id` = yc.`id`
                   LEFT JOIN `sys_org_bill` bill ON `sc`.`id` = bill.`biz_id` and bill.second_biz_type=2
          WHERE `sc`.`org_id` = #{orgId}
            AND `sc`.`status` = 2
          <if test="teacherId != null">
              AND `sc`.`teacher_id` = #{teacherId}
          </if>
          <if test="childId != null">
              AND `sc`.`child_id` = #{childId}
          </if>
          <if test="queryStartDate != null">
              AND DATE(`sc`.`end_time`) >= DATE(#{queryStartDate})
          </if>
          <if test="queryEndDate != null">
              AND DATE(`sc`.`end_time`) &lt;= DATE(#{queryEndDate})
          </if>
      </if>
      <if test="courseType == null">
          UNION ALL
      </if>
      <if test="courseType== null or (courseType != null and courseType == 3)">
          -- mcc_course 子查询
          SELECT 3                                             AS `course_type`,
                 `mc`.`id`                                     AS `course_id`,
                 `mc`.`org_id`                                 AS `org_id`,
                 `so`.`name`                                   AS `org_name`,
                 `so`.`short_name`                             AS `org_short_name`,
                 `mc`.`teacher_id`                             AS `teacher_id`,
                 `mc`.`teacher_name`                           AS `teacher_name`,
                 `mc`.`start_time`                             AS `start_time`,
                 `mc`.`end_time`                               AS `end_time`,
                 group_concat(`mcp`.`person_id` SEPARATOR ',') AS `child_ids`,
                 group_concat(yc.`name`)                       AS `child_names`,
                 bill.`amount`      AS `amount`
          FROM `mcc_course` `mc`
                   LEFT JOIN `sys_org` `so` ON `mc`.`org_id` = `so`.`id`
                   LEFT JOIN `mcc_course_person` `mcp` ON `mcp`.`course_id` = `mc`.`id` AND `mcp`.`type` = 3
                   LEFT JOIN `ycx_children` yc ON FIND_IN_SET(yc.id, `mcp`.`person_id`)
                   LEFT JOIN `sys_org_bill` bill ON `mc`.`id` = bill.`biz_id` and bill.second_biz_type=3
          WHERE `mc`.`org_id` = #{orgId}
            AND `mc`.`status` = 2
          <if test="teacherId != null">
              AND `mc`.`teacher_id` = #{teacherId}
          </if>
          <if test="childId != null">
              AND `mcp`.`person_id` = #{childId}
          </if>
          <if test="queryStartDate != null">
              AND DATE(`mc`.`end_time`) >= DATE(#{queryStartDate})
          </if>
          <if test="queryEndDate != null">
              AND DATE(`mc`.`end_time`) &lt;= DATE(#{queryEndDate})
          </if>
          GROUP BY `mc`.`id`
      </if>

      ) vac
      ORDER BY vac.start_time DESC, vac.course_type
  </select>

   <select id="getOrgWalletInfo" resultType="com.bmh.project.analysis.vo.AnalysisOrgWalletlVo">
       SELECT
           o.id AS orgId,
           IFNULL(w.balance,0) AS balance,
           IFNULL(w.recharge_amount,0) AS rechargeAmount,
           IFNULL(w.consum_amount,0) AS consumAmount,
           IFNULL(w.withdrawal_amount,0) AS withdrawalAmount,
           IFNULL(invoice_amount,0) AS invoiceAmount
       FROM  sys_org o
       LEFT JOIN  sys_org_wallet w on o.id = w.org_id
       where o.id =  #{orgId}
       order by w.status desc
       limit 1
    </select>

    <select id="getOrgBillList" resultType="com.bmh.project.analysis.vo.AnalysisOrgBillVo">
        SELECT
        bill.id as id,
        bill.org_id as orgId,
        bill.bill_type as  billType,
        bill.amount as amount,
        bill.pay_type as  payType,
        bill.first_biz_type as firstBizType,
        bill.second_biz_type as secondBizType,
        bill.trade_date as tradeDate,
        case when first_biz_type=1 then '充值'
        when first_biz_type=2 then '提现'
        when first_biz_type=3 and second_biz_type =1  then '扣除：综合能力'
        when first_biz_type=3 and second_biz_type =2  then '扣除：言语训练'
        when first_biz_type=3 and second_biz_type =3  then '扣除：音乐课'
        else '其他费用' end  as  billBizName
        from sys_org_bill bill
        where  bill.org_id = #{orgId}
        <if test="firstBizType != null">
            AND  bill.first_biz_type = #{firstBizType}
        </if>
        ORDER BY trade_date DESC
    </select>

</mapper>