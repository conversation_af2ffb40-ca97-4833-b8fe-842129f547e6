<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.ame.mapper.YcxAmeSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.ame.model.YcxAmeSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="ame_id" jdbcType="INTEGER" property="ameId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

    <select id="getSelecteds" parameterType="Integer" resultMap="BaseResultMap">
      select
        sele.*,
        ame.content,
        ame.order_num,
        op.name as optionName
      from
        ycx_ame_selected as sele
          inner join
        ycx_ame as ame
        on
          ame.id = sele.ame_id
          left join
        ycx_ame_option as op
        on
            op.ame_id = ame.id
            and
            op.value = sele.answer
      where
        sele.record_id = #{recordId}
      order by ame.order_num asc
    </select>
</mapper>