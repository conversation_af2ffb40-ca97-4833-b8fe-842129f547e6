<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.snap.mapper.YcxSnapMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.snap.model.YcxSnap">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="order_num" jdbcType="INTEGER" property="orderNum"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>

        <collection property="options" ofType="com.bmh.project.evaluation.snap.model.YcxSnapOption">
            <result column="snap_id" property="snapId"/>
            <result column="name" property="name"/>
            <result column="value" property="value"/>
            <result column="score" property="score"/>
        </collection>
    </resultMap>

    <select id="getSnapListByType" resultMap="BaseResultMap">
        select snap.*
             , op.snap_id
             , op.name
             , op.value
             , op.score
        from ycx_snap as snap
                     inner join ycx_snap_option as op
                on op.snap_id = snap.id
        where snap.type = #{type}
          and snap.status = 1
        order by snap.order_num asc, op.id asc
    </select>
</mapper>