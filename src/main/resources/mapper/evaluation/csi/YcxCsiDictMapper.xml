<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.csi.mapper.YcxCsiDictMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.csi.model.YcxCsiDict">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="orig_score" jdbcType="VARCHAR" property="origScore" />
    <result column="real_score" jdbcType="VARCHAR" property="realScore" />
  </resultMap>
</mapper>