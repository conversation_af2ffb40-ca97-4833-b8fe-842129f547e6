<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.analysis.mapper.AnalysisChildRtMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.analysis.model.AnalysisChildRt">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="course_type" jdbcType="INTEGER" property="courseType" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="course_count" jdbcType="INTEGER" property="courseCount" />
    <result column="course_time" jdbcType="BIGINT" property="courseTime" />
    <result column="last_course_time" jdbcType="TIMESTAMP" property="lastCourseTime" />
    <result column="last_supervise_time" jdbcType="TIMESTAMP" property="lastSuperviseTime" />
  </resultMap>

  <select id="getRtList" resultType="com.bmh.project.analysis.vo.AnalysisChildRtVo">
      SELECT yc.id                                                            AS childId,
             yc.`name`                                                        AS childName,
             yc.link_mobile                                                   AS linkMobile,
             yc.logo                                                          AS childLogo,
             yc.org_id                                                        AS orgId,
             so.`name`                                                        AS orgName,
             so.short_name                                                    AS orgShortName,
             MAX(IF(vacr.course_type = 1, COALESCE(vacr.course_count, 0), 0)) AS abaCount,
             MAX(IF(vacr.course_type = 2, COALESCE(vacr.course_count, 0), 0)) AS stCount,
             MAX(IF(vacr.course_type = 3, COALESCE(vacr.course_count, 0), 0)) AS mccCount,
             SUM(COALESCE(vacr.course_time, 0))                               AS courseTime,
             MAX(vacr.last_course_time)                                       AS lastCourseTime,
             MAX(vacr.last_supervise_time)                                    AS lastSuperviseTime
      FROM ycx_children yc
               LEFT JOIN sys_org so ON yc.org_id = so.id
               LEFT JOIN view_analysis_child_rt vacr ON vacr.child_id = yc.id
      <where>
          <if test="orgId != null">
              AND yc.org_id = #{orgId}
          </if>
          <if test="childName != null and childName != ''">
              AND yc.`name` LIKE CONCAT('%', #{childName}, '%')
          </if>
      </where>
      GROUP BY yc.id
      ORDER BY lastCourseTime DESC
  </select>
</mapper>