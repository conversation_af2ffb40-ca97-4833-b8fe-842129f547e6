<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaCourseProjectGoalMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaCourseProjectGoal">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="teacher_id" jdbcType="INTEGER" property="teacherId" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="plan_project_id" jdbcType="INTEGER" property="planProjectId" />
    <result column="plan_project_goal_id" jdbcType="INTEGER" property="planProjectGoalId" />
    <result column="course_project_id" jdbcType="INTEGER" property="courseProjectId" />
    <result column="short_goal_id" jdbcType="INTEGER" property="shortGoalId" />
    <result column="short_goal_name" jdbcType="VARCHAR" property="shortGoalName" />
    <result column="assist_method" jdbcType="VARCHAR" property="assistMethod" />
    <result column="record_type" jdbcType="INTEGER" property="recordType" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="target" jdbcType="LONGVARCHAR" property="target" />
  </resultMap>
</mapper>