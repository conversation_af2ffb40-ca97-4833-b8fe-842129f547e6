<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.ybocs.mapper.YcxYbocsSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.ybocs.model.YcxYbocsSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="ybcos_id" jdbcType="INTEGER" property="ybcosId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="staus" jdbcType="INTEGER" property="staus" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
    <select id="getSelecteds" resultMap="BaseResultMap">
      select sele.*,
      ybcos.content,
      ybcos.order_num,
      op.name as optionName
      from
        ycx_ybocs_selected as sele
          inner join
        ycx_ybocs as ybcos
        on
          ybcos.id = sele.ybcos_id
          left join
        ycx_ybocs_option as op
        on
            op.ybcos_id = ybcos.id
            and
            op.value = sele.answer
      where
        sele.record_id = 1
      order by ybcos.order_num asc
    </select>
</mapper>