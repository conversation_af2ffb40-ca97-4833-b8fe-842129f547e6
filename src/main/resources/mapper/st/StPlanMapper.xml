<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.st.mapper.StPlanMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.st.model.StPlan">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
    <select id="getLastPlanId" resultType="java.lang.Integer">
        select id from st_plan where child_id = #{childId} order by create_time desc limit 1
    </select>
    <select id="selectEndSupervisor" resultType="java.lang.Integer">
        SELECT
        sp.child_id
        FROM
        st_plan sp
        INNER JOIN (SELECT child_id, MAX(create_time) AS create_time
        FROM st_plan
        WHERE child_id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY child_id) s
        ON sp.child_id = s.child_id
        AND sp.create_time = s.create_time
    </select>
</mapper>
