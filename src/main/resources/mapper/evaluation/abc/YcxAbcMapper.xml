<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.abc.mapper.YcxAbcMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.abc.model.YcxAbc">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />



    <collection property="options" ofType="com.bmh.project.evaluation.abc.model.YcxAbcOption" >
      <result column="abc_id" jdbcType="INTEGER" property="abcId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="value" jdbcType="VARCHAR" property="value" />
    </collection>

  </resultMap>


<!--  List<YcxAbc> getList();-->
  <select id="getList" resultMap="BaseResultMap" >
    select abc.*
    , op.abc_id
    , op.name
    , op.value
    from ycx_abc as abc
    left join ycx_abc_option as op
    on op.abc_id = abc.id
    order by abc.order_num asc,op.value desc, op.id asc
  </select>

</mapper>