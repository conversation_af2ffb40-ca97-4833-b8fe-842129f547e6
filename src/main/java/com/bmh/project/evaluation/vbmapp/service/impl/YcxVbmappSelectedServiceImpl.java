package com.bmh.project.evaluation.vbmapp.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.evaluation.vbmapp.dto.YcxVbmappSelectedDto;
import com.bmh.project.evaluation.vbmapp.mapper.YcxVbmappSelectedMapper;
import com.bmh.project.evaluation.vbmapp.model.YcxVbmappSelected;
import com.bmh.project.evaluation.vbmapp.service.YcxVbmappSelectedService;
import javax.annotation.Resource;

import com.bmh.project.evaluation.vbmapp.vo.ChildNoteVo;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * YcxVbmappSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2022年11月22日 17:57:14
 */
@Service
public class YcxVbmappSelectedServiceImpl extends BaseServiceImpl<YcxVbmappSelected> implements YcxVbmappSelectedService {
    @Resource
    private YcxVbmappSelectedMapper ycxVbmappSelectedMapper;

    @Override
    public Integer submitAnswer(YcxVbmappSelectedDto dto) {
        Integer userId = SecurityUtil.getUserId();
        String nickName = SecurityUtil.getNickName();

        YcxVbmappSelected selected = new YcxVbmappSelected();
        selected.setSubjectId(dto.getSubjectId());
        selected.setChildId(dto.getChildId());
        selected.setKeyword(dto.getKeyword());
        selected.setStatus(1);
        selected.setCreateTime(new Date());
        selected.setOperatorId(userId);
        selected.setOperatorName(nickName);
        selected.setOrgId(SecurityUtil.getOrgId());
        //ToDo
        //selected.setSubjectName();//项目名称
        return this.insert(selected);
        //TODO 计算分数

    }

    @Override
    public Integer deleteSelected(Integer selectedId) {
        YcxVbmappSelected selected = new YcxVbmappSelected();
        selected.setId(selectedId);
        selected.setStatus(0);
        selected.setDeleteTime(new Date());
        return this.updateNotNull(selected);
    }

    @Override
    public ChildNoteVo getChildNote(Integer subjectId, Integer childId) {
        Example example=new Example(YcxVbmappSelected.class);
        example.createCriteria().andEqualTo("status",1)
                .andEqualTo("subjectId",subjectId)
                .andEqualTo("childId",childId);
        example.orderBy("id").asc();
        List<YcxVbmappSelected> list = this.selectByExample(example);

        List<String> collect = list.stream().map(p -> p.getKeyword())
                .distinct().collect(Collectors.toList());
        ChildNoteVo vo = new ChildNoteVo();
        vo.setSelectedList(list);
        vo.setQuantity(collect.size());
        return vo;
    }
}