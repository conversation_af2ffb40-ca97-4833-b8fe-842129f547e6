package com.bmh.project.evaluation.ybocs.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.ybocs.mapper.YcxYbocsSelectedMapper;
import com.bmh.project.evaluation.ybocs.model.YcxYbocsSelected;
import com.bmh.project.evaluation.ybocs.service.YcxYbocsSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxYbocsSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月17日 11:17:05
 */
@Service
public class YcxYbocsSelectedServiceImpl extends BaseServiceImpl<YcxYbocsSelected> implements YcxYbocsSelectedService {
    @Resource
    private YcxYbocsSelectedMapper ycxYbocsSelectedMapper;

    @Override
    public void saveSelected(List<YcxYbocsSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreatedTime(new Date());
            op.setStaus(1);
            op.setUpdateTime(new Date());
        });
        insertList(selecteds);
    }

    @Override
    public List<YcxYbocsSelected> getSelecteds(Integer recordId) {
        return ycxYbocsSelectedMapper.getSelecteds(recordId);
    }


}