package com.bmh.project.evaluation.ygtss.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.record.model.YcxChildrenRecord;
import com.bmh.project.evaluation.ygtss.mapper.YcxYgtssResultMapper;
import com.bmh.project.evaluation.ygtss.model.YcxYgtssResult;
import com.bmh.project.evaluation.ygtss.model.YcxYgtssSelected;
import com.bmh.project.evaluation.ygtss.service.YcxYgtssResultService;

import javax.annotation.Resource;

import com.bmh.project.evaluation.ygtss.service.YcxYgtssSelectedService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * YcxYgtssResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月13日 13:48:54
 */
@Service("34")
public class YcxYgtssResultServiceImpl extends BaseServiceImpl<YcxYgtssResult> implements YcxYgtssResultService, YcxResultService<YcxYgtssResult> {
    @Resource
    private YcxYgtssResultMapper ycxYgtssResultMapper;
    @Resource
    private YcxYgtssSelectedService ycxYgtssSelectedService;

    /**
     * 保存评测结果
     *
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxYgtssResult result = JSONUtil.toBean(childrenRecord.getResultStr(), YcxYgtssResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge());
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOpetatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date());
        ycxYgtssResultMapper.insert(result);

        List<YcxYgtssSelected> selecteds = result.getSelecteds();
        ycxYgtssSelectedService.saveSelected(selecteds, result.getRecordId());
    }

    /**
     * 获取评测结果
     *
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxYgtssResult getResult(Integer recordId) {
        Example example = new Example(YcxYgtssResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxYgtssResult> list = this.selectByExample(example);
        if (list != null && list.size() > 0) {
            YcxYgtssResult result = list.get(0);
            result.setSelecteds(ycxYgtssSelectedService.getSelected(recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome(Integer recordId, String recom) {
        YcxYgtssResult result = new YcxYgtssResult();
        result.setRecome(recom);
        Example example = new Example(YcxYgtssResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        this.updateByExampleSelective(result, example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}