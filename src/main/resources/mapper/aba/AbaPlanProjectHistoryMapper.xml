<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaPlanProjectHistoryMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaPlanProjectHistory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="batch_no" jdbcType="INTEGER" property="batchNo" />
    <result column="plan_project_id" jdbcType="INTEGER" property="planProjectId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_level" jdbcType="INTEGER" property="projectLevel" />
    <result column="long_goal" jdbcType="VARCHAR" property="longGoal" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
  </resultMap>

  <select id="getMaxBatchNo" resultType="java.lang.Integer">
    select
    max(batch_no)
    from aba_plan_project_history where plan_id = #{planId}
  </select>

  <select id="getProjectLastModifyDate" resultType="java.util.Date">
    select max(create_time)
    from aba_plan_project_history
    where plan_project_id = #{planProjectId}
      and type = 1
      and date(create_time) &lt;= date(#{deadline})
  </select>

  <select id="getProjectLastHistory" resultMap="BaseResultMap">
    select *
    from aba_plan_project_history
    where plan_project_id = #{planProjectId}
      and date(create_time) &lt;= date(#{deadline})
    order by create_time desc
    limit 1
  </select>

  <select id="getDomainLastModifyDate" resultType="java.util.Date">
    select max(apph.create_time)
    from aba_plan_project_history apph
    left join aba_plan_project app on apph.plan_project_id = app.id
    left join aba_plan_project_goal gl on app.id = gl.plan_project_id
    where app.domain_id = #{domainId}
    and date(apph.create_time) &lt;= date(#{deadline})
     and gl.record_type = 1
  </select>
</mapper>