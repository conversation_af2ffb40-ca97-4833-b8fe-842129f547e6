package com.bmh.project.evaluation.ybocs.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.ybocs.model.YcxYbocs;
import com.bmh.project.evaluation.ybocs.service.YcxYbocsService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Y-BOCS 耶鲁布朗强迫症严重程度量表
 */
@RestController
@RequestMapping("/ybocs")
public class YcxBocsController {

    @Resource
    private YcxYbocsService ycxYbocsService;

    /**
     * 获取所有问题
     * @return
     */
    @RequestMapping("/getYbocs")
    public Result<List<YcxYbocs>> getYbocs(){
        List<YcxYbocs> list = ycxYbocsService.getYbocs();
        return ResultUtil.success(list);
    }
}
