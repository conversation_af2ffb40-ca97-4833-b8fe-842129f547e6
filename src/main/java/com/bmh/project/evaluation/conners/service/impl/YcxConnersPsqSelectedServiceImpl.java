package com.bmh.project.evaluation.conners.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.conners.mapper.YcxConnersPsqSelectedMapper;
import com.bmh.project.evaluation.conners.model.YcxConnersPsqSelected;
import com.bmh.project.evaluation.conners.service.YcxConnersPsqSelectedService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxConnersPsqSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月10日 14:43:46
 */
@Service
public class YcxConnersPsqSelectedServiceImpl extends BaseServiceImpl<YcxConnersPsqSelected> implements YcxConnersPsqSelectedService {
    @Resource
    private YcxConnersPsqSelectedMapper ycxConnersPsqSelectedMapper;

    /**
     * 保存选择项
     *
     * @param selecteds 选择项
     * @param recordId  记录ID
     */
    @Override
    public void saveSelecteds (List<YcxConnersPsqSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date ());
            op.setStatus(1);
        });
        ycxConnersPsqSelectedMapper.insertList(selecteds);
    }
}