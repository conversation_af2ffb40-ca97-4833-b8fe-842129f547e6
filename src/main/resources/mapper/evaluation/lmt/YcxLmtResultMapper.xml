<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.lmt.mapper.YcxLmtResultMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.lmt.model.YcxLmtResult">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="children_age" jdbcType="VARCHAR" property="childrenAge" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="level" jdbcType="INTEGER" property="orgId" />
    <result column="level_explain" jdbcType="VARCHAR" property="levelExplain" />
    <result column="level_recom" jdbcType="VARCHAR" property="levelRecom" />
    <result column="recom" jdbcType="VARCHAR" property="recom" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="prel_result_id" jdbcType="INTEGER" property="prelResultId" />
    <result column="prel_result_level" jdbcType="INTEGER" property="prelResultLevel" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>