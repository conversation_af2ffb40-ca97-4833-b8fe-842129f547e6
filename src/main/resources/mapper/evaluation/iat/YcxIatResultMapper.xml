<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.iat.mapper.YcxIatResultMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.iat.model.YcxIatResult">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="children_age" jdbcType="VARCHAR" property="childrenAge" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="result_his" jdbcType="LONGVARCHAR" property="resultHis" />
  </resultMap>
</mapper>