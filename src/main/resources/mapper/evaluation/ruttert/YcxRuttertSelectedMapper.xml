<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.ruttert.mapper.YcxRuttertSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.ruttert.model.YcxRuttertSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="ruttert_id" jdbcType="INTEGER" property="ruttertId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <select id="getSelecteds" resultMap="BaseResultMap">
    select sele.*,
      ruttert.content,
      ruttert.order_num,
      op.name as optionName
      from
        ycx_ruttert_selected as sele
          inner join
        ycx_ruttert as ruttert
        on
          ruttert.id = sele.ruttert_id
          left join
        ycx_ruttert_option as op
        on
            op.ruttert_id = ruttert.id
            and
            op.value = sele.answer
      where
        sele.record_id = #{recordId}
      order by ruttert.order_num asc
  </select>
</mapper>