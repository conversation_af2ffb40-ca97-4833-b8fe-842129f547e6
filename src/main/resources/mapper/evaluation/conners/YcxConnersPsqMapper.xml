<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.conners.mapper.YcxConnersPsqMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.conners.model.YcxConnersPsq">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.conners.model.YcxConnersPsqOption">
      <result column="psq_id" property="psqId"/>
      <result column="name" property="name"/>
      <result column="value" property="value"/>
      <result column="score" property="score"/>
    </collection>
  </resultMap>

  <select id="getListByType" resultMap="BaseResultMap">
    select ycp.*
    , op.psq_id
    , op.name
    , op.value
    , op.score
    from ycx_conners_psq as ycp
    inner join ycx_conners_psq_option as op
    on op.psq_id = ycp.id
    where ycp.type = #{type}
    and ycp.status = 1
    order by ycp.order_num asc, op.id asc
  </select>
</mapper>