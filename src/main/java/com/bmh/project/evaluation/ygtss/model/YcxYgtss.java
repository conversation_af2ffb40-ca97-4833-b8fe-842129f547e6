package com.bmh.project.evaluation.ygtss.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_ygtss")
public class YcxYgtss implements Serializable {
    /**
     * 主键Id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1抽动情况，2抽动评价)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 题目内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 排序
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 状态
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 题目等级
     */
    @Column(name = "grade")
    private Integer grade;

    /**
     * 对应的父类题目
     */
    @Column(name = "parentId")
    private Integer parentid;

    /**
     * 一级题目对应的内容
     */
    @Column(name = "note")
    private String note;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;

    private static final long serialVersionUID = 1L;

    /**
     * 选项
     */
    @Transient
    private List<YcxYgtssOption> options;

    /**
     * 所对应的子问题
     */
    @Transient
    private List<YcxYgtss> childygtss;

}