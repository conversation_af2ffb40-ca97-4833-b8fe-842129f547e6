<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.analysis.mapper.AbaAnalysisDomainMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.analysis.model.AbaAnalysisDomain">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="domain_rate" jdbcType="DOUBLE" property="domainRate" />
    <result column="is_adjust" jdbcType="INTEGER" property="isAdjust" />
  </resultMap>

  <select id="getList" resultMap="BaseResultMap">
      select *
      from aba_analysis_domain
      where child_id = #{childId}
      <if test="firstDate != null">
          and date >= #{firstDate}
      </if>
      order by `date`, domain_id
  </select>

  <select id="getRecentlyDate" resultType="java.util.Date">
      select date
      from aba_analysis_domain
      where child_id = #{childId}
      group by date
      order by date desc
      limit #{range}
  </select>
</mapper>