<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.nyad.mapper.YcxNyadSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.nyad.model.YcxNyadSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="nyad_id" jdbcType="INTEGER" property="nyadId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
    <select id="getSelecteds" resultMap="BaseResultMap">
      select
        sele.*,
        nyad.content,
        nyad.order_num,
        op.name as optionName
      from
        ycx_nyad_selected as sele
          inner join
        ycx_nyad as nyad
        on
          nyad.id = sele.nyad_id
          left join
        ycx_nyad_option as op
        on
            op.nyad_id = nyad.id
            and
            op.value = sele.answer
      where
        sele.record_id = #{recordId}
      order by nyad.order_num asc
    </select>
</mapper>