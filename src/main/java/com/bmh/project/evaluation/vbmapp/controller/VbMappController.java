package com.bmh.project.evaluation.vbmapp.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.vbmapp.dto.YcxVbmappSelectedDto;
import com.bmh.project.evaluation.vbmapp.model.YcxVbmappRealm;
import com.bmh.project.evaluation.vbmapp.model.YcxVbmappSubject;
import com.bmh.project.evaluation.vbmapp.service.YcxVbmappRealmService;
import com.bmh.project.evaluation.vbmapp.service.YcxVbmappResultService;
import com.bmh.project.evaluation.vbmapp.service.YcxVbmappSelectedService;
import com.bmh.project.evaluation.vbmapp.service.YcxVbmappSubjectService;
import com.bmh.project.evaluation.vbmapp.vo.ChildNoteVo;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;

/**
 * VB-MAPP 接口
 */
@RestController
@RequestMapping("/vbMapp")
public class VbMappController {

    @Resource
    private YcxVbmappRealmService realmService;

    @Resource
    private YcxVbmappSubjectService subjectService;

    @Resource
    private YcxVbmappSelectedService selectedService;

    @Resource
    private YcxVbmappResultService resultService;

    /**
     * 获取领域及里程碑列表
     * @return
     */
    @GetMapping("getRealmList")
    public Result<List<YcxVbmappRealm>> getRealmList() {
        List<YcxVbmappRealm> list = realmService.getRealmList();
        return ResultUtil.success(list);
    }

    /**
     * 获取快捷记录列表
     * @return
     */
    @GetMapping("getQuickList")
    public Result<List<YcxVbmappRealm>> getQuickList() {
        List<YcxVbmappRealm> list = realmService.getQuickRealmList();
        return ResultUtil.success(list);
    }

    /**
     * 获取测试题目
     * @param realmId
     * @param pageNum
     * @return
     */
    @GetMapping("getSubject")
    public Result getSubjectByRealm(Integer realmId,Integer pageNum){
        if(pageNum==null || pageNum<1){
            pageNum=1;
        }
        PageHelper.startPage (pageNum,1);
        List<YcxVbmappSubject> list = subjectService.getSubjectByRealm(realmId);
        PageInfo page = new PageInfo(list);
        return ResultUtil.success(page);
    }

    /**
     * 提交答案
     * @param dto
     * @return
     */
    @PostMapping("submitAnswer")
    public Result submitAnswer(@RequestBody YcxVbmappSelectedDto dto){
        Integer i= selectedService.submitAnswer(dto);
        return ResultUtil.success(i);
    }

    /**
     * 获取答题记录列表
     * @param subjectId
     * @param childId
     * @return
     */
    @GetMapping("getChildNote")
    public Result<ChildNoteVo> getChildNote(Integer subjectId, Integer childId){
        ChildNoteVo vo = selectedService.getChildNote(subjectId,childId);
        return ResultUtil.success(vo);
    }

    /**
     * 删除记录
     * @param selectedId
     * @return
     */
    @PostMapping("deleteSelected")
    public Result deleteSelected(Integer selectedId){
        Integer i = selectedService.deleteSelected(selectedId);
        return ResultUtil.success(i);
    }

    /**
     * 更改分数
     * @param subjectId
     * @param childId
     * @param score
     * @return
     */
    @PostMapping("changeScore")
    public Result<Integer> changeScore(Integer subjectId, Integer childId, BigDecimal score){
        Integer i = resultService.changeScore(subjectId,childId,score);
        return ResultUtil.success(i);
    }

    /**
     * 获取分数
     * @param subjectId
     * @param childId
     * @return
     */
    @GetMapping("getScore")
    public Result<String> getScore(Integer subjectId, Integer childId){
        String score = resultService.getScore(subjectId,childId);
        return ResultUtil.success(score);
    }
}
