package com.bmh.project.evaluation.behavior.controller;


import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.behavior.model.YcxBehavior;
import com.bmh.project.evaluation.behavior.service.YcxBehaviorResultService;
import com.bmh.project.evaluation.behavior.service.YcxBehaviorService;
import com.bmh.project.evaluation.gesell.model.YcxGesell;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 0-6岁儿童发育行为评估
 */
@RestController
@RequestMapping("behavior")
public class YcxBehaviorController {


    @Resource
    private YcxBehaviorService behaviorService;
    @Resource
    private YcxBehaviorResultService behaviorResultService;


    /**
     * 根据类型获取题库
     * @param type 类型(1大运动，2精细动作，3适应能力，4语言，5社会行为)
     * @return
     */
    @RequestMapping("getBehaviors")
    public Result getBehaviors(Integer type){
        List<YcxBehavior> list = behaviorService.getBehaviors(type);
        return ResultUtil.success(list);
    }


}
