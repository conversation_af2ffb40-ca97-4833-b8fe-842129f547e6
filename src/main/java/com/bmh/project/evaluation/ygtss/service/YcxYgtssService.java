package com.bmh.project.evaluation.ygtss.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.ygtss.model.YcxYgtss;

import java.util.List;

/**
 * ycx_ygtss表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月13日 13:48:54
 */
public interface YcxYgtssService extends BaseService<YcxYgtss> {

    /**
     * 根据类型获取问题
     *
     * @param type 类型(1.抽动情况，2.抽动评价)
     * @return
     */
    List<YcxYgtss> getYgtssByType(Integer type);
}