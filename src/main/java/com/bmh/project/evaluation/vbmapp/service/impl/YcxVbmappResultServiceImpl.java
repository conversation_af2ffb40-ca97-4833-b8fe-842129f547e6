package com.bmh.project.evaluation.vbmapp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.vbmapp.mapper.YcxVbmappResultMapper;
import com.bmh.project.evaluation.vbmapp.model.YcxVbmappResult;
import com.bmh.project.evaluation.vbmapp.service.YcxVbmappResultService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.List;

/**
 * YcxVbmappResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2022年11月22日 17:57:14
 */
@Service
public class YcxVbmappResultServiceImpl extends BaseServiceImpl<YcxVbmappResult> implements YcxVbmappResultService {
    @Resource
    private YcxVbmappResultMapper ycxVbmappResultMapper;

    @Override
    public Integer changeScore(Integer subjectId, Integer childId, BigDecimal score) {
        Example example = new Example(YcxVbmappResult.class);
        example.createCriteria().andEqualTo("subjectId",subjectId)
                .andEqualTo("childId",childId);
        YcxVbmappResult result = new YcxVbmappResult();
        result.setScore(score);
        return this.updateByExampleSelective(result, example);
    }

    @Override
    public String getScore(Integer subjectId, Integer childId) {
        Example example = new Example(YcxVbmappResult.class);
        example.createCriteria().andEqualTo("subjectId",subjectId)
                .andEqualTo("childId",childId)
                .andEqualTo("status",1);
        List<YcxVbmappResult> list = this.selectByExample(example);
        if(CollectionUtil.isNotEmpty(list)){
            return list.get(0).getScore().toString();
        }
        return null;
    }
}