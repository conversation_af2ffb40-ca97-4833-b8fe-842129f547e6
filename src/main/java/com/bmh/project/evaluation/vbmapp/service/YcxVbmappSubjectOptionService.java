package com.bmh.project.evaluation.vbmapp.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.vbmapp.model.YcxVbmappSubjectOption;

import java.util.List;

/**
 * ycx_vbmapp_subject_option表对应的Service接口
 *
 * <AUTHOR>
 * @date 2022年11月21日 20:20:23
 */
public interface YcxVbmappSubjectOptionService extends BaseService<YcxVbmappSubjectOption> {
    /**
     * 获取题目选项
     * @param subjectId
     * @return
     */
    List<YcxVbmappSubjectOption> getSubjectOption(Integer subjectId);
}