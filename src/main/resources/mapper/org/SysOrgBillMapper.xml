<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.org.mapper.SysOrgBillMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.org.model.SysOrgBill">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="bill_type" jdbcType="INTEGER" property="billType" />
    <result column="bill_no" jdbcType="VARCHAR" property="billNo" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="wallet_amount" jdbcType="DECIMAL" property="walletAmount" />
    <result column="pay_type" jdbcType="INTEGER" property="payType" />
    <result column="first_biz_type" jdbcType="INTEGER" property="firstBizType" />
    <result column="second_biz_type" jdbcType="INTEGER" property="secondBizType" />
    <result column="biz_id" jdbcType="INTEGER" property="bizId" />
    <result column="trade_date" jdbcType="TIMESTAMP" property="tradeDate" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="certificate" jdbcType="VARCHAR" property="certificate" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>