<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.mcc.mapper.MccOrgLessonPlanMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.mcc.model.MccOrgLessonPlan">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="teacher_id" jdbcType="INTEGER" property="teacherId" />
    <result column="teacher_name" jdbcType="VARCHAR" property="teacherName" />
    <result column="lesson_plan_id" jdbcType="INTEGER" property="lessonPlanId" />
    <result column="lesson_plan_name" jdbcType="VARCHAR" property="lessonPlanName" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="getList" resultType="com.bmh.project.mcc.vo.MccOrgLessonPlanVo">
      select olp.id,
             olp.org_id           as orgId,
             olp.teacher_id       as teacherId,
             olp.teacher_name     as teacherName,
             olp.lesson_plan_id   as lessonPlanId,
             olp.lesson_plan_name as lessonPlanName,
             olp.create_time      as createTime,
             max(mc.start_time)   as lastCourseStartTime,
             max(mc.end_time)     as lastCourseEndTime
      from mcc_org_lesson_plan olp
               left join mcc_course mc on olp.id = mc.org_lesson_plan_id
      where olp.org_id = #{orgId}
        and olp.is_delete = 0
      group by olp.id
      order by olp.create_time desc
  </select>
</mapper>