<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.lmt.mapper.YcxLmtPrelSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.lmt.model.YcxLmtPrelSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="result_id" jdbcType="INTEGER" property="resultId" />
    <result column="lmt_prel_id" jdbcType="INTEGER" property="lmtPrelId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="getDomainScoreList" resultType="com.bmh.project.evaluation.lmt.vo.YcxLmtPrelDomainResultVo">
      select ylp.domain_id as domainId, ylp.domain_name as domainName, sum(ylps.score) as score
      from ycx_lmt_prel_selected ylps
               left join ycx_lmt_prel ylp on ylps.lmt_prel_id = ylp.id
      where ylps.result_id = #{resultId}
      and ylps.status = 1
      group by ylp.domain_id
  </select>
</mapper>