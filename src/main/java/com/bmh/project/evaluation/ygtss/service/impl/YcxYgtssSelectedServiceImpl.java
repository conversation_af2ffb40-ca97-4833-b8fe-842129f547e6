package com.bmh.project.evaluation.ygtss.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.ygtss.mapper.YcxYgtssSelectedMapper;
import com.bmh.project.evaluation.ygtss.model.YcxYgtssSelected;
import com.bmh.project.evaluation.ygtss.service.YcxYgtssSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxYgtssSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月13日 13:48:54
 */
@Service
public class YcxYgtssSelectedServiceImpl extends BaseServiceImpl<YcxYgtssSelected> implements YcxYgtssSelectedService {
    @Resource
    private YcxYgtssSelectedMapper ycxYgtssSelectedMapper;

    /**
     * 保存选择项
     * @param selecteds
     * @param recordId
     */
    @Override
    public void saveSelected(List<YcxYgtssSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        this.insertList(selecteds);
    }

    /**
     * 获取评测结果
     * @param recordId
     * @return
     */
    @Override
    public List<YcxYgtssSelected> getSelected(Integer recordId) {
        return ycxYgtssSelectedMapper.getSelected(recordId);
    }
}