package com.bmh.project.evaluation.ybocs.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;

@Data
@Table(name = "ycx_ybocs_result")
public class YcxYbocsResult extends YcxResult implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录id
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 孩子年龄
     */
    @Column(name = "children_age")
    private BigDecimal childrenAge;

    /**
     * 孩子id
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 单位id
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 用于展示结果说明的字符串
     */
    @Column(name = "result_his")
    private String resultHis;

    /**
     * 评测结果
     */
    @Column(name = "result")
    private String result;

    /**
     * 备注
     */
    @Column(name = "remarks")
    private String remarks;

    /**
     * 评测结果
     */
    @Column(name = "recom")
    private String recom;

    /**
     * 操作人id
     */
    @Column(name = "operatr_id")
    private Integer operatrId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "creare_time")
    private Date creareTime;

    private static final long serialVersionUID = 1L;

    /**
     * 选项
     */
    @Transient
    private List<YcxYbocsSelected> selecteds;
}