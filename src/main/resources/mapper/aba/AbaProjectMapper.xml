<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaProjectMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaProject">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="long_goal" jdbcType="VARCHAR" property="longGoal" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="getEffectiveList" resultType="com.bmh.project.aba.vo.AbaProjectVo">
      select ap.id,
             ap.domain_id   as domainId,
             ap.domain_name as domainName,
             ap.name,
             ap.level
      from aba_project ap
      where ap.domain_id = #{domainId}
      <if test="projectName != null and projectName != ''">
          and ap.name like concat('%', #{projectName}, '%')
      </if>
      order by ap.level
  </select>

    <select id="getExecuteList" resultType="java.lang.Integer">
        select distinct pj.project_id as id
        from aba_plan_project pj
        inner  join aba_plan_project_goal gl on pj.id = gl.plan_project_id  and gl.record_type =  #{recordType}
        where pj.domain_id =  #{domainId}
        and pj.plan_id = #{planId}
        and gl.status =  1
        and pj.status =  1
    </select>

    <select id="getNearPassList" resultType="com.bmh.project.aba.vo.AbaProjectVo">
        select pj.project_id as id,
        pj.`status`,
        pj.update_time AS lastSuspendTime,
        pj.update_user AS lastSuspendUser
        from aba_plan_project pj
        inner  join aba_plan_project_goal gl on pj.id = gl.plan_project_id  and gl.record_type =  #{recordType}
        where pj.domain_id =  #{domainId}
        and pj.plan_id = #{planId}
        and pj.status >  1
        order by pj.id desc
    </select>

  <select id="getLmtDefalutProjectList" resultMap="BaseResultMap">
      select id, domain_id, domain_name, name, level from
          aba_project where id in (
          SELECT MIN(id) FROM aba_project WHERE domain_id &lt;= 5 AND `level` = #{level} GROUP BY domain_id
          )
    </select>
    <select id="getGoalDetailByGoalId" resultType="com.bmh.project.aba.vo.AbaPlanProjectVo">
        SELECT
            ap.domain_name AS domainName,
            ap.`level` AS projectLevel,
            ap.`name` AS projectName,
            apg.short_goal AS shortGoalName,
            apg.operate_method AS operateMethod
        FROM
            aba_project_goal apg
                INNER JOIN aba_project ap ON apg.project_id = ap.id
        WHERE
            apg.id = #{id}
    </select>
</mapper>
