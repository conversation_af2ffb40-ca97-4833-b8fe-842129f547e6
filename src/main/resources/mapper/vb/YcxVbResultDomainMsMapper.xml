<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.vb.mapper.YcxVbResultDomainMsMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.vb.model.YcxVbResultDomainMs">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="result_id" jdbcType="INTEGER" property="resultId" />
    <result column="result_domain_id" jdbcType="INTEGER" property="resultDomainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>