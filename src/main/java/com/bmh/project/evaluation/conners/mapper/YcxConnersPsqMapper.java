package com.bmh.project.evaluation.conners.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.evaluation.conners.model.YcxConnersPsq;
import com.bmh.project.evaluation.snap.model.YcxSnap;

import java.util.List;

public interface YcxConnersPsqMapper extends BaseMapper<YcxConnersPsq> {

    /**
     * 根据类型获取问题
     * @param type 类型(1品行问题，2学习问题，3心身问题，4冲动-多动， 5焦虑，6其他)
     * @return
     */
    List<YcxConnersPsq> getListByType(Integer type);
}