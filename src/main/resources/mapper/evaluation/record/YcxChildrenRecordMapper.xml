<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.record.mapper.YcxChildrenRecordMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.record.model.YcxChildrenRecord">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="record_number" jdbcType="VARCHAR" property="recordNumber"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="org_id" jdbcType="INTEGER" property="orgId"/>
        <result column="children_id" jdbcType="INTEGER" property="childrenId"/>
        <result column="children_age" jdbcType="DECIMAL" property="childrenAge"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_id" jdbcType="INTEGER" property="createId"/>
        <result column="create_name" jdbcType="VARCHAR" property="createName"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime"/>


        <result column="orgName" jdbcType="VARCHAR" property="orgName"/>
        <result column="projectName" jdbcType="VARCHAR" property="projectName"/>
        <result column="projectShortName" jdbcType="VARCHAR" property="projectShortName"/>
    </resultMap>

    <!--  YcxChildrenRecord selectRecordDetail(Integer recordId);-->
    <select id="selectRecordDetail" parameterType="Integer" resultMap="BaseResultMap">
        select record.*
             , org.name as orgName
        from ycx_children_record as record
                     left join sys_org as org
                on org.id = record.org_id
        where record.id = #{recordId}
    </select>

    <select id="getRecordListByChildId" resultMap="BaseResultMap">
        select ycr.id,
               ycr.record_number,
               ycr.type,
               ycr.org_id,
               ycr.children_id,
               ycr.children_age,
               ycr.status,
               ycr.create_id,
               ycr.create_name,
               ycr.create_time,
               ycr.submit_time,
               yp.title as projectName,
                yp.short_title as projectShortName
        from ycx_children_record ycr
        left join ycx_project yp on ycr.type = yp.id
        where ycr.status = 1
        and ycr.children_id = #{childrenId}
        and ycr.org_id = #{orgId}
        order by ycr.submit_time desc
    </select>
<!--  特殊通道使用  -->
    <select id="getChannelEvaluatingList" resultType="com.bmh.project.record.vo.ChannelEvaluatingVo">
        SELECT ep.id AS recordId,
               ep.evaluating_id AS evaluatingId,
               ep.result_img AS resultImg,
               ep.recommend,
               ep.submit_time,
               p.tag,
               p.title,
               p.short_title AS shortTitle
        FROM ycx_children_evaluating_project ep
                 LEFT JOIN ycx_children_evaluating e ON e.id= ep.evaluating_id
                 LEFT JOIN ycx_project p ON ep.project_id = p.id
                 LEFT JOIN ycx_children c ON c.id=e.children_id
        WHERE ep.`status`=2 AND ep.is_delete = 0 AND c.link_mobile=#{phone}
    </select>
    <select id="getChannelRecordList" resultType="com.bmh.project.record.vo.ChannelRecordVo">
        SELECT
            ep.evaluating_id AS evaluatingId,
            e.record_number AS recordNumber,
            e.org_id AS orgId,
            e.doctor_id AS doctorId,
            e.children_id AS childrenId,
            e.submit_time AS submitTime,
            d.name AS doctorName
        FROM ycx_children_evaluating_project ep
                 LEFT JOIN ycx_children_evaluating e ON e.id= ep.evaluating_id
                 LEFT JOIN ycx_children c ON c.id=e.children_id
                 LEFT JOIN sys_user d ON d.id=e.doctor_id
        WHERE ep.`status`=2 AND ep.is_delete = 0 AND c.link_mobile=#{phone}
        GROUP BY ep.evaluating_id
        ORDER BY e.submit_time DESC
    </select>
</mapper>