<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaCourseRecordMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaCourseRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="course_project_id" jdbcType="INTEGER" property="courseProjectId" />
    <result column="course_project_goal_id" jdbcType="INTEGER" property="courseProjectGoalId" />
    <result column="plan_project_goal_id" jdbcType="INTEGER" property="planProjectGoalId" />
    <result column="short_goal_id" jdbcType="INTEGER" property="shortGoalId" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="target_name" jdbcType="VARCHAR" property="targetName" />
    <result column="target_color" jdbcType="VARCHAR" property="targetColor" />
    <result column="target_done" jdbcType="INTEGER" property="targetDone" />
    <result column="assist_method" jdbcType="INTEGER" property="assistMethod" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />

  </resultMap>

  <select id="calcPassCount" resultType="com.bmh.project.aba.vo.AbaCourseGoalTargetTotalVo">
      select gl.short_goal_id  targetName,
      SUM(IF(rd.target_done = 1, 1, 0)) AS passCount,
      SUM(IF(rd.target_done = 0, 1, 0)) AS failCount,
      SUM(1) AS totalCount
      from aba_course_record rd
      INNER JOIN aba_course_project_goal  gl  on  rd.course_project_goal_id = gl.id
      where rd.course_project_goal_id = gl.id
      and rd.status = 1
      and rd.course_project_id =  #{courseProjectId}
      <if test="courseProjectGoalId != null">
          AND rd.course_project_goal_id = #{courseProjectGoalId}
      </if>
      group by  gl.short_goal_id
  </select>



  <select id="getListByPlanProjectId" resultMap="BaseResultMap">
      SELECT acr.*
      FROM aba_course_record acr
               LEFT JOIN aba_course_project acp ON acp.id = acr.course_project_id
      WHERE acp.plan_project_id = #{planProjectId}
      and acr.status = 1
      ORDER BY acr.target_name, acr.create_time
  </select>

    <select id="getListByPlanProjectIds" resultMap="BaseResultMap">
        SELECT acr.*
        FROM aba_course_record acr
                 LEFT JOIN aba_course_project acp ON acp.id = acr.course_project_id
        WHERE acr.status = 1
          and acp.plan_project_id  in
        <foreach collection="planProjectIds" item="planProjectId" open="(" separator="," close=")">
            #{planProjectId}
        </foreach>
        ORDER BY  acr.create_time
    </select>

    <select id="calcTargetPassCount" resultType="com.bmh.project.aba.vo.AbaCourseGoalTargetTotalVo">
        select target_name as targetName,
               target_color as targetColor,
        SUM(IF(rd.target_done = 1, 1, 0)) AS passCount,
        SUM(IF(rd.target_done = 0, 1, 0)) AS failCount,
        SUM(1) AS totalCount
        from aba_course_record rd
        INNER JOIN aba_course_project_goal  gl  on  rd.course_project_goal_id = gl.id
        where rd.course_project_goal_id = gl.id
        and rd.status = 1
        and rd.course_project_id =  #{courseProjectId}
        <if test="courseProjectGoalId != null">
            AND rd.course_project_goal_id = #{courseProjectGoalId}
        </if>
        group by target_name , target_color
    </select>

    <select id="getRecordCount" resultType="java.lang.Integer">
        SELECT COUNT(acr.id)
        FROM aba_course_record acr
        LEFT JOIN aba_course ac ON acr.course_id = ac.id
        WHERE acr.status = 1
          AND acr.create_time &gt; #{startTime}
          AND acr.create_time &lt; #{endTime}
          <if test="orgId!=0">
            AND ac.org_id = #{orgId}
          </if>
        <if test="0==orgId">
            AND ac.org_id NOT IN (1,19,43,65)
        </if>
    </select>

    <select id="getListByPlanProjectGoalIds" resultType="com.bmh.project.aba.vo.AbaTargetPassRateVo">
        SELECT acr.target_name as name,sum(case when acr.target_done = 1 then 1 else 0 end) as passCount,
        count(acr.target_name) as projectMinRound
        FROM aba_course_record acr
        WHERE acr.status = 1
        and acr.course_project_goal_id  in
        <foreach collection="planProjectGoalIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        group  by acr.target_name
    </select>
</mapper>
