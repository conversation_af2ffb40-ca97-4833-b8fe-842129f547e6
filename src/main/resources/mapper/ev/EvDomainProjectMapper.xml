<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ev.mapper.EvDomainProjectMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.ev.model.EvDomainProject">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="domain_id" jdbcType="INTEGER" property="domainId"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="short_title" jdbcType="VARCHAR" property="shortTitle"/>
        <result column="no" jdbcType="INTEGER" property="no"/>
        <result column="stage" jdbcType="VARCHAR" property="stage"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="aba_domain" jdbcType="VARCHAR" property="abaDomain"/>
        <result column="description" jdbcType="VARCHAR" property="description"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="is_delete" jdbcType="INTEGER" property="isDelete"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="create_by" jdbcType="VARCHAR" property="createBy"/>
        <result column="update_by" jdbcType="VARCHAR" property="updateBy"/>
        <result column="score" jdbcType="DECIMAL" property="score" />
        <result column="opinion" jdbcType="LONGVARCHAR" property="opinion"/>
    </resultMap>
    <select id="selectByType" resultType="com.bmh.project.ev.model.EvDomainProject">
        SELECT id, title, domain_id AS domainId, no,stage,score,short_title as shortTitle,opinion
        FROM ev_domain_project
        WHERE `status` = 1 AND is_delete = 0 AND type = #{type}
    </select>
</mapper>
