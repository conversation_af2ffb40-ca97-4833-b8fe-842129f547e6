package com.bmh.project.evaluation.gesell.controller;


import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.gesell.model.YcxGesell;
import com.bmh.project.evaluation.gesell.service.YcxGesellResultService;
import com.bmh.project.evaluation.gesell.service.YcxGesellService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import java.util.List;

/**
 * Gesell发育诊断量表（GDS）4周~6岁
 */
@RestController
@RequestMapping("gesell")
public class YcxGesellController {


    @Autowired
    private YcxGesellService gesellService;
    @Autowired
    private YcxGesellResultService gesellResultService;

    /**
     * 根据分类获取题库
     * @param type 类型(1适应性行为，2大运动，3精细动作，4语言，5个人-社会性行为)
     * @return
     */
    @RequestMapping("getGeSellByType")
    public Result getGeSellByType(Integer type){
        List<YcxGesell> list = gesellService.getGeSellByType(type);
        return ResultUtil.success(list);
    }





}
