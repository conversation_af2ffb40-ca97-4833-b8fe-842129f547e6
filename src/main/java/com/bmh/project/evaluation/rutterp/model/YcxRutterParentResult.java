package com.bmh.project.evaluation.rutterp.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;

@Data
@Table(name = "ycx_rutterp_result")
public class YcxRutterParentResult extends YcxResult implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录id
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 孩子年龄
     */
    @Column(name = "children_age")
    private BigDecimal childrenAge;

    /**
     * 孩子id
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 单位id
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 用于展示结果说明的字符串
     */
    @Column(name = "result_his")
    private String resultHis;

    /**
     * 评测结果
     */
    @Column(name = "result")
    private String result;

    /**
     * 建议
     */
    @Column(name = "recome")
    private String recome;

    /**
     * 操作人id
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;

    private static final long serialVersionUID = 1L;

    /**
     * 选中项
     */
    @Transient
    private List<YcxRutterParentSelected> selecteds;

    @Column(name = "wj_result")
    private Integer wjResult;

    @Column(name = "sjz_result")
    private Integer sjzResult;

}