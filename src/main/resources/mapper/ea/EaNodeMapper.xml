<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ea.mapper.EaNodeMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.ea.model.EaNode">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="grade_type" jdbcType="INTEGER" property="gradeType"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="start_time" jdbcType="TIME" property="startTime"/>
        <result column="end_time" jdbcType="TIME" property="endTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    </resultMap>

    <resultMap id="ListResultMap" type="com.bmh.project.ea.model.EaNode">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="grade_type" jdbcType="INTEGER" property="gradeType"/>
        <result column="icon" jdbcType="VARCHAR" property="icon"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="start_time" jdbcType="TIME" property="startTime"/>
        <result column="end_time" jdbcType="TIME" property="endTime"/>

        <collection property="subNodeList" ofType="com.bmh.project.ea.model.EaSubNode">
            <id column="sub_node_id" property="id"/>
            <result column="node_id" property="nodeId"/>
            <result column="sub_node_name" property="name"/>
            <result column="full_name" property="fullName"/>
            <result column="standard" property="standard"/>
            <result column="order_num" property="orderNum"/>
        </collection>
    </resultMap>
    <select id="getList" resultMap="ListResultMap">
        select en.*,
               esn.id   as sub_node_id,
               esn.node_id,
               esn.name as sub_node_name,
               esn.full_name as full_name,
               esn.standard,
               esn.order_num
        from ea_node en
                 left join ea_sub_node esn on en.id = esn.node_id and esn.status = 1
        where en.status = 1
        and en.grade_type = #{gradeType}
        order by en.start_time, esn.order_num
    </select>
</mapper>