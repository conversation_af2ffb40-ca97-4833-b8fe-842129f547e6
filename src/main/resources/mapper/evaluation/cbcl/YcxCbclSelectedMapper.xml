<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.cbcl.mapper.YcxCbclSelectedMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.cbcl.model.YcxCbclSelected">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="record_id" jdbcType="INTEGER" property="recordId"/>
        <result column="cbcl_id" jdbcType="INTEGER" property="cbclId"/>
        <result column="answer" jdbcType="VARCHAR" property="answer"/>
        <result column="score" jdbcType="INTEGER" property="score"/>
        <result column="text1" jdbcType="VARCHAR" property="text1"/>
        <result column="text2" jdbcType="VARCHAR" property="text2"/>
        <result column="text3" jdbcType="VARCHAR" property="text3"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>

        <result column="cbclType" jdbcType="INTEGER" property="cbclType"/>
        <result column="cbclContent" jdbcType="VARCHAR" property="cbclContent"/>
        <result column="optionName" jdbcType="VARCHAR" property="optionName"/>
        <result column="cbclOrderNum" jdbcType="INTEGER" property="cbclOrderNum"/>
    </resultMap>

    <select id="getSelecteds" resultMap="BaseResultMap">
        select ycs.*, yc.type as cbclType, yc.content as cbclContent, yco.name as optionName, yc.order_num as
        cbclOrderNum
        from ycx_cbcl_selected ycs
        left join ycx_cbcl yc on ycs.cbcl_id = yc.id
        left join ycx_cbcl_option yco on ycs.cbcl_id = yco.cbcl_id and ycs.answer = yco.value
        where ycs.record_id = #{recordId}
    </select>
</mapper>