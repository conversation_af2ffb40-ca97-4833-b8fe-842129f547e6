<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.vb.mapper.YcxVbResultMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.vb.model.YcxVbResult">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="result_date" jdbcType="DATE" property="resultDate" />
    <result column="result_user" jdbcType="VARCHAR" property="resultUser" />
    <result column="result_user_id" jdbcType="INTEGER" property="resultUserId" />
    <result column="total_score" jdbcType="DECIMAL" property="totalScore" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="parent_feedback" jdbcType="LONGVARCHAR" property="parentFeedback" />
    <result column="parent_hope" jdbcType="LONGVARCHAR" property="parentHope" />
    <result column="assess_question" jdbcType="LONGVARCHAR" property="assessQuestion" />
    <result column="prereq_skill" jdbcType="LONGVARCHAR" property="prereqSkill" />
    <result column="assess_result" jdbcType="LONGVARCHAR" property="assessResult" />
    <result column="advance" jdbcType="LONGVARCHAR" property="advance" />
    <result column="referral_suggestion" jdbcType="LONGVARCHAR" property="referralSuggestion" />
  </resultMap>
    <select id="selectHistoryList" resultType="com.bmh.project.report.vo.ReportLMTTitleVo">
        select id as recordId, "VBMAPP" as shortTitle, "VBMAPP评估" as title, result_date as createdTime
        from ycx_vb_result
        where children_id = #{id}
          and status = 1
    </select>
</mapper>
