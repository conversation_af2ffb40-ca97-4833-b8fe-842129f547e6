<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.gesell.mapper.YcxGesellMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.gesell.model.YcxGesell">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="tips" jdbcType="VARCHAR" property="tips" />
    <result column="special" jdbcType="VARCHAR" property="special" />
    <result column="month" jdbcType="INTEGER" property="month" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="example_img" jdbcType="VARCHAR" property="exampleImg" />
    <result column="props" jdbcType="VARCHAR" property="props" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>


<!--  List<YcxGesell> getGeSellByType(Integer type);-->
  <select id="getGeSellByType" parameterType="int" resultMap="BaseResultMap" >
    select gesell.*
    from ycx_gesell as gesell
    where gesell.type = #{type}
    and gesell.status = 1
    order by gesell.month asc
  </select>

</mapper>