spring.profiles.active=dev
spring.resources.static-locations=classpath:/resources/,classpath:/static/

#-------------------Server---------------------------------
server.tomcat.uri-encoding=UTF-8
server.servlet.context-path=/
server.tomcat.connection-timeout=1200000
#-------------------Server---------------------------------

#-------------------Druid---------------------------------
spring.datasource.type=com.alibaba.druid.pool.DruidDataSource
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
# 配置检测连接是否有效
spring.datasource.druid.testWhileIdle=true
spring.datasource.druid.testOnBorrow=false
spring.datasource.druid.testOnReturn=false
spring.datasource.druid.validationQuery=SELECT 1 FROM DUAL
spring.datasource.druid.webStatFilter.enabled=true
spring.datasource.druid.statViewServlet.enabled=true
spring.datasource.druid.statViewServlet.allow=
spring.datasource.druid.statViewServlet.url-pattern=/druid/*
spring.datasource.druid.statViewServlet.login-username=druid
spring.datasource.druid.statViewServlet.login-password=adminDruid9876
spring.datasource.druid.filter.stat.enabled=true
# 慢SQL记录
spring.datasource.druid.filter.stat.log-slow-sql=true
spring.datasource.druid.filter.stat.slow-sql-millis=2000
spring.datasource.druid.filter.stat.merge-sql=true
spring.datasource.druid.filter.wall.config.multi-statement-allow=true
#-------------------Druid---------------------------------

#-------------------Mybatis---------------------------------
mybatis.mapper-locations=classpath:mapper/**/*.xml
mapper.not-empty=false
mapper.identity=MYSQL
pagehelper.helperDialect=mysql
pagehelper.reasonable=true
pagehelper.params=count\=countSql
pagehelper.support-methods-arguments=true
#-------------------Mybatis---------------------------------

#-------------------Jackson---------------------------------
spring.jackson.serialization.write-dates-as-timestamps=true
spring.jackson.deserialization.accept-single-value-as-array=true
#-------------------Jackson---------------------------------

#-------------------JWT---------------------------------
token.header=Authorization
token.secret=abcdefghijklmnopqrstuvwxyzzzzsss
token.expireTime=604800
#-------------------JWT---------------------------------

#-------------------QINIU---------------------------------
file.access.key=xcnO9mDsteyK3vz-QnD3V_rHqCBtI9psG0GAW0SQ
file.secret.key=ZIdXf-ejf6bKuSgYczBbtB1iFO58iQPyHS0TGX5K
file.bucket=baifenxiao
file.domain=https://file.baifenxiao.com
#-------------------QINIU---------------------------------

#------------------- sms4j ---------------------------------
sms.config-type=yaml
sms.is-print=false
sms.restricted=true
sms.account-max=10
sms.minute-max=1
sms.blends.xinyang.supplier=xinyang
sms.blends.xinyang.sdk-app-id=4953
sms.blends.xinyang.access-key-id=bjsbjk
sms.blends.xinyang.access-key-secret=Songbai@135
sms.blends.xinyang.signature=【青松成长】
#------------------- sms4j ---------------------------------
