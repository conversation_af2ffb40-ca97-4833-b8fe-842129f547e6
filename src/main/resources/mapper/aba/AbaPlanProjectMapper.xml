<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaPlanProjectMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaPlanProject">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_level" jdbcType="INTEGER" property="projectLevel" />
    <result column="long_goal" jdbcType="VARCHAR" property="longGoal" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="opt_type" jdbcType="INTEGER" property="optType" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
  </resultMap>

  <select id="getPrepPlanProjectList" resultType="com.bmh.project.aba.vo.AbaPrepPlanProjectVo">
      select app.id,
             app.org_id as orgId,
             app.child_id as childId,
             app.plan_id as planId,
             app.domain_id as domainId,
             app.domain_name as domainName,
             app.project_id as projectId,
             app.project_name as projectName,
             app.project_level as projectLevel,
             app.long_goal as longGoal,
      if(appr.prep_status is null, 0, appr.prep_status) as prepStatus
      from aba_plan_project app
               left join aba_prep apr on apr.plan_id = #{planId} and apr.teacher_id = #{teacherId} and
                                         apr.prep_date = date(#{prepDate})
               left join aba_prep_project appr on appr.plan_project_id = app.id and appr.prep_id = apr.id
      where app.plan_id = #{planId}
        and app.status = 1

  </select>

  <select id="getAbaPassRankList" resultType="com.bmh.project.analysis.vo.AnalysisChildPassRankVo">
      SELECT
          app.org_id AS orgId,
          app.child_id AS childId,
          yc.`name` AS childName,
          COUNT(app.id) AS passCount
      FROM
          `aba_plan_project` app
              LEFT JOIN ycx_children yc ON app.child_id = yc.id
      WHERE
          app.`status` = 2
      <if test="orgId != null">
          AND app.org_id = #{orgId}
      </if>
      <choose>
          <when test="precondition != null and precondition == 1">
              AND DATE(app.end_time) = CURRENT_DATE
          </when>
          <when test="precondition != null and precondition == 2">
              AND DATE(app.end_time) = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)
          </when>
          <when test="precondition != null and precondition == 3">
              AND YEARWEEK(app.end_time, 1) = YEARWEEK(NOW(), 1)
          </when>
          <when test="precondition != null and precondition == 4">
              AND DATE(app.end_time) >= DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY)
          </when>
      </choose>
      <if test="queryStartDate != null">
          AND DATE(app.end_time) >= DATE(#{queryStartDate})
      </if>
      <if test="queryEndDate != null">
          AND DATE(app.end_time) &lt;= DATE(#{queryEndDate})
      </if>
      GROUP BY
          app.child_id
      ORDER BY passCount DESC
    </select>

  <select id="getPlanProjectAnalysisByState" resultType="com.bmh.project.aba.vo.AbaPlanProjectAnalysisByStateVo">
      SELECT COUNT(DISTINCT app.project_id)                 AS totalCount,
             SUM(IF(app.status = 1, 1, 0)) AS progressCount,
             SUM(IF(app.status = 2, 1, 0)) AS passCount,
             SUM(IF(app.status = 3, 1, 0)) AS stopCount
      FROM aba_plan_project app
      WHERE app.plan_id = #{planId}
  </select>

  <select id="getPlanProjectAnalysisByDomain" resultType="com.bmh.project.aba.vo.AbaPlanProjectAnalysisByDomainVo">
      select ad.id         as domainId,
             ad.name       as domainName,
             COUNT(DISTINCT ap.id)  as projectCount,
             COUNT(DISTINCT app.project_id) as planProjectCount
      from aba_domain ad
               left join aba_project ap on ad.id = ap.domain_id
               left join aba_plan_project app on ad.id = app.domain_id and app.plan_id = #{planId}
      group by ad.id
      order by ad.id
  </select>

    <select id="getPlanShortGoalListByChildId" resultType="com.bmh.project.aba.vo.AbaPlanProjectVo">
        SELECT distinct p.`id` as id,
        p.`org_id` as orgId,
        p.`child_id` as childId,
        p.`plan_id`as planId,
        p.`domain_id` as domianId,
        p.`domain_name` as domainName,
        p.`project_id` as projectId,
        p.`project_name` as projectName,
        p.`project_level` as projectLevel,
        p.`long_goal` as longGoal,
        g.`short_goal_id` as shortGoalId,
        g.`short_goal_name` as shortGoalName,
        g.`assist_method` as assistMethod,
        g.`target` as target,
        p.`start_time` as startTime,
        p.`end_time` as endTime,
        g.`record_type` as recordType,
        p.`opt_type` as optType,
        p.`status` as status ,
        p.`create_user` as createUser,
        p.`create_time` as createTime,
        p.`update_time` as updateTime,
        p.`update_user` as updateUser,
        p.`remarks` as remarks
        from aba_plan_project p
        INNER JOIN aba_plan_project_goal g on p.id = g.plan_project_id and g.record_type = 1
        where p.child_id = #{childId}
        and IFNULL(g.`short_goal_id`,'')!=''
        <if test="statusStr != null">
            and p.`status` in (#{statusStr})
        </if>

        <if test="shortGoalIds != null">
            and  g.`short_goal_id` in
            <foreach collection="shortGoalIds" item="goalIds" open="(" separator="," close=")">
                #{goalIds}
            </foreach>
        </if>
        order by p.domain_id,p.`status`
    </select>

    <select id="getPlanProjectList" resultType="com.bmh.project.aba.vo.AbaPlanProjectVo">
        SELECT DISTINCT
            app.domain_id AS domainId,
            app.domain_name AS domainName,
            app.project_id AS projectId,
            app.project_name AS projectName,
            app.project_level AS projectLevel,
            appg.id as planProjectShortGoalId
        FROM
            aba_plan_project app
                INNER JOIN aba_plan_project_goal appg ON app.id = appg.plan_project_id
        WHERE
            app.plan_id = #{planId}
          <if test="status!=null">
              AND appg.`status` = #{status}
          </if>
        ORDER BY
            app.domain_id
    </select>
    <select id="getAllProjectList" resultType="com.bmh.project.aba.model.AbaProject">
        select
            id ,
            name,
            level,
            domain_id AS domainId,
            domain_name AS domainName
        from aba_project
        order by domain_id,id
    </select>


</mapper>
