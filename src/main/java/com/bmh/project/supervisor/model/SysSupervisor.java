package com.bmh.project.supervisor.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "sys_supervisor")
public class SysSupervisor implements Serializable {
    /**
     * 主键
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 手机号
     */
    @Column(name = "mobile")
    private String mobile;

    /**
     * 督导姓名
     */
    @Column(name = "name")
    private String name;

    /**
     * 密码
     */
    @Column(name = "password")
    private String password;

    /**
     * 状态(0禁用 1启用)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 是否是内部人员(0否 1是)
     */
    @Column(name = "is_inside")
    private Integer isInside;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}