<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.ybocs.mapper.YcxYbocsMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.ybocs.model.YcxYbocs">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.ybocs.model.YcxYbocsOption" >
    <result column="ybocs_id" jdbcType="INTEGER" property="ybcosId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="score" jdbcType="VARCHAR" property="score" />
    </collection>
  </resultMap>
    <select id="getYbocs" resultMap="BaseResultMap">
      select ybocs.*
        , op.ybcos_id
        , op.name
        , op.value
        , op.score
      from ycx_ybocs as ybocs
      left join ycx_ybocs_option as op
      on
        op.ybcos_id = ybocs.id
      order by
        ybocs.order_num asc,
        op.score asc
    </select>
</mapper>