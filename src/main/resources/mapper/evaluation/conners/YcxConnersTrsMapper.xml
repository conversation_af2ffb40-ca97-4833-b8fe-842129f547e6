<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.conners.mapper.YcxConnersTrsMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.conners.model.YcxConnersTrs">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.conners.model.YcxConnersTrsOption">
      <result column="trs_id" property="trsId"/>
      <result column="name" property="name"/>
      <result column="value" property="value"/>
      <result column="score" property="score"/>
    </collection>
  </resultMap>

  <select id="getListByType" resultMap="BaseResultMap">
    select yct.*
    , op.trs_id
    , op.name
    , op.value
    , op.score
    from ycx_conners_trs as yct
    inner join ycx_conners_trs_option as op
    on op.trs_id = yct.id
    where yct.type = #{type}
    and yct.status = 1
    order by yct.order_num asc, op.id asc
  </select>
</mapper>