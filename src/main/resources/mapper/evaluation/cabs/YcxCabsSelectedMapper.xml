<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.cabs.mapper.YcxCabsSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.cabs.model.YcxCabsSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="cabs_id" jdbcType="INTEGER" property="cabsId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />


    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="optionName" jdbcType="VARCHAR" property="optionName" />

  </resultMap>


<!--  List<YcxCabsSelected> getSelecteds(Integer recordId);-->
  <select id="getSelecteds" parameterType="Integer" resultMap="BaseResultMap" >
    select sele.*
    , cabs.content
    , cabs.order_num
    , op.name as optionName
    from ycx_cabs_selected as sele
    inner join ycx_cabs as cabs
    on cabs.id = sele.cabs_id
    left join ycx_cabs_option as op
    on op.cabs_id = cabs.id
    and op.value = sele.answer
    where sele.record_id = #{recordId}
    order by cabs.order_num asc
  </select>

</mapper>