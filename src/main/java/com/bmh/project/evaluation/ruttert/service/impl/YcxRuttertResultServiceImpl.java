package com.bmh.project.evaluation.ruttert.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.record.model.YcxChildrenRecord;
import com.bmh.project.evaluation.ruttert.mapper.YcxRuttertResultMapper;
import com.bmh.project.evaluation.ruttert.model.YcxRuttertResult;
import com.bmh.project.evaluation.ruttert.service.YcxRuttertResultService;
import javax.annotation.Resource;

import com.bmh.project.evaluation.ruttert.service.YcxRuttertSelectedService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * YcxRuttertResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月17日 09:39:00
 */
@Service("39")
public class YcxRuttertResultServiceImpl extends BaseServiceImpl<YcxRuttertResult> implements YcxRuttertResultService, YcxResultService<YcxRuttertResult> {
    @Resource
    private YcxRuttertResultMapper ycxRuttertResultMapper;
    @Resource
    private YcxRuttertSelectedService ruttertSelectedService;

    /**
     * 保存评测结果
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxRuttertResult result = JSONUtil.toBean(childrenRecord.getResultStr(), YcxRuttertResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge());
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setCreatedTime(new Date());
        ycxRuttertResultMapper.insert(result);

        ruttertSelectedService.saveSelected(result.getSelecteds(),result.getRecordId());
    }

    /**
     * 获取评测结果
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxRuttertResult getResult(Integer recordId) {
        Example example = new Example(YcxRuttertResult.class);
        example.createCriteria().andEqualTo("recordId",recordId);
        List<YcxRuttertResult> list = selectByExample(example);
        if (list != null && list.size() > 0){
            YcxRuttertResult result = list.get(0);
            result.setSelecteds(ruttertSelectedService.getSelecteds(recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     * @param recordId 记录ID
     * @param recom 建议
     */
    @Override
    public void updateRecome(Integer recordId, String recom) {
        YcxRuttertResult ruttertResult = new YcxRuttertResult();
        ruttertResult.setRecome(recom);
        Example example = new Example(YcxRuttertResult.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("recordId",recordId);
        ycxRuttertResultMapper.updateByExampleSelective(ruttertResult,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}