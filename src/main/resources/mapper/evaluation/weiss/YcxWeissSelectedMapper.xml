<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.weiss.mapper.YcxWeissSelectedMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.weiss.model.YcxWeissSelected">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="record_id" jdbcType="INTEGER" property="recordId"/>
        <result column="weiss_id" jdbcType="INTEGER" property="weissId"/>
        <result column="answer" jdbcType="VARCHAR" property="answer"/>
        <result column="score" jdbcType="INTEGER" property="score"/>
        <result column="remark" jdbcType="VARCHAR" property="remark"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>

        <result column="weissType" jdbcType="INTEGER" property="weissType"/>
    </resultMap>

    <select id="getSelecteds" resultMap="BaseResultMap">
        select yws.id,
               yws.record_id,
               yws.weiss_id,
               yws.answer,
               yws.score,
               yws.remark,
               yws.status,
               yws.create_time,
               yw.type as weissType
        from ycx_weiss_selected yws
                     left join ycx_weiss yw on yws.weiss_id = yw.id
        where yws.record_id = #{recordId}
          and yws.status = 1
    </select>
</mapper>