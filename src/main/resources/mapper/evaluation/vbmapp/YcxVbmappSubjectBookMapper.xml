<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.vbmapp.mapper.YcxVbmappSubjectBookMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.vbmapp.model.YcxVbmappSubjectBook">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="subject_id" jdbcType="INTEGER" property="subjectId" />
    <result column="label" jdbcType="VARCHAR" property="label" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="type" jdbcType="INTEGER" property="type" />
  </resultMap>
</mapper>