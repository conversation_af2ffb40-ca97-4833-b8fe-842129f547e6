<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.ecaa.mapper.YcxEcaaResultMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.ecaa.model.YcxEcaaResult">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="children_age" jdbcType="VARCHAR" property="childrenAge" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="res_type1" jdbcType="INTEGER" property="resType1" />
    <result column="res_type2" jdbcType="INTEGER" property="resType2" />
    <result column="res_type3" jdbcType="INTEGER" property="resType3" />
    <result column="res_type4" jdbcType="INTEGER" property="resType4" />
    <result column="res_type5" jdbcType="INTEGER" property="resType5" />
    <result column="res_type6" jdbcType="INTEGER" property="resType6" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="recom" jdbcType="VARCHAR" property="recom" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="result_his" jdbcType="LONGVARCHAR" property="resultHis" />
    <result column="result_chart" jdbcType="LONGVARCHAR" property="resultChart" />
  </resultMap>
</mapper>