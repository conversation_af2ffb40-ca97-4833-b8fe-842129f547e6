<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.adosg.mapper.YcxAdosgSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.adosg.model.YcxAdosgSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="adosg_id" jdbcType="INTEGER" property="adosgId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />


    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="optionName" jdbcType="VARCHAR" property="optionName" />

  </resultMap>



<!--  List<YcxChatSelected> selectSelectedByType(Integer type, Integer recordId);-->
  <select id="selectSelectedByType" parameterType="Integer" resultMap="BaseResultMap" >
    select sele.*
    , adosg.content
    , adosg.order_num
    , op.name as optionName
    from ycx_adosg_selected as sele
    inner join ycx_adosg as adosg
    on adosg.id = sele.adosg_id
    left join ycx_adosg_option as op
    on op.adosg_id = adosg.id
    and op.value = sele.answer
    where sele.record_id = #{recordId}
    and adosg.type = #{type}
    order by adosg.order_num asc
  </select>

</mapper>