package com.bmh.project.evaluation.ybocs.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.ybocs.model.YcxYbocsSelected;

import java.util.List;

/**
 * ycx_ybocs_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月17日 11:17:05
 */
public interface YcxYbocsSelectedService extends BaseService<YcxYbocsSelected> {

    void saveSelected(List<YcxYbocsSelected> selecteds, Integer recordId);

    List<YcxYbocsSelected> getSelecteds(Integer recordId);
}