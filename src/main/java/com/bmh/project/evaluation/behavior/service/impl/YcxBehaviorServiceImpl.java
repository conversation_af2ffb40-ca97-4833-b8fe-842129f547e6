package com.bmh.project.evaluation.behavior.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.behavior.mapper.YcxBehaviorMapper;
import com.bmh.project.evaluation.behavior.model.YcxBehavior;
import com.bmh.project.evaluation.behavior.service.YcxBehaviorService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxBehaviorService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月15日 12:21:59
 */
@Service
public class YcxBehaviorServiceImpl extends BaseServiceImpl<YcxBehavior> implements YcxBehaviorService {
    @Resource
    private YcxBehaviorMapper ycxBehaviorMapper;

    @Override
    public List<YcxBehavior> getBehaviors(Integer type) {
        return ycxBehaviorMapper.getBehaviors(type);
    }


}