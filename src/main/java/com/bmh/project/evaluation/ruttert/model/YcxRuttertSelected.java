package com.bmh.project.evaluation.ruttert.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_ruttert_selected")
public class YcxRuttertSelected implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 评测记录id
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 问题id
     */
    @Column(name = "ruttert_id")
    private Integer ruttertId;

    /**
     * 选项
     */
    @Column(name = "answer")
    private String answer;

    /**
     * 分数
     */
    @Column(name = "score")
    private Integer score;

    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}