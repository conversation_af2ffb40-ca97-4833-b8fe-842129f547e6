<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.tas.mapper.YcxTasSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.tas.model.YcxTasSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="tas_id" jdbcType="INTEGER" property="tasId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

    <select id="getSelecteds" parameterType="Integer" resultMap="BaseResultMap">
      select
        sele.*,
        tas.content,
        tas.order_num,
        op.name as optionName
      from
        ycx_tas_selected as sele
          inner join
        ycx_tas as tas
        on
          tas.id = sele.tas_id
          left join
        ycx_tas_option as op
        on
            op.tas_id = tas.id
            and
            op.value = sele.answer
      where
        sele.record_id = #{recordId}
      order by tas.order_num asc
    </select>
</mapper>