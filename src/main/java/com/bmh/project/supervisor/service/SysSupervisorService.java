package com.bmh.project.supervisor.service;

import com.bmh.common.base.BaseService;
import com.bmh.common.security.model.LoginBody;
import com.bmh.project.supervisor.model.SysSupervisor;

import java.util.Map;

/**
 * sys_supervisor表对应的Service接口
 *
 * <AUTHOR>
 * @date 2023年07月24日 11:21:32
 */
public interface SysSupervisorService extends BaseService<SysSupervisor> {

    /**
     * 查询登录督导
     * @param loginBody 登录参数
     * @return
     */
    SysSupervisor getSupervisorByLoginBody(LoginBody loginBody);

    /**
     * 获取督导人员的机构用户模拟身份
     * @param orgId 需要模拟的机构ID
     * @return
     */
    Map<String, Object> getMockSysUser (Integer orgId);
}