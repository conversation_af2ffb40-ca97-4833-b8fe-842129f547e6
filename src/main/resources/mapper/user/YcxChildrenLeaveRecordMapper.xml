<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.user.mapper.YcxChildrenLeaveRecordMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.user.model.YcxChildrenLeaveRecord">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="org_id" jdbcType="INTEGER" property="orgId"/>
        <result column="child_id" jdbcType="INTEGER" property="childId"/>
        <result column="leave_date" jdbcType="DATE" property="leaveDate"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    </resultMap>

    <select id="getList" resultMap="BaseResultMap">
        SELECT r1.id,
               r1.org_id,
               r1.child_id,
               r1.leave_date,
               r1.status
        FROM ycx_children_leave_record r1
                 INNER JOIN (SELECT child_id,
                                    leave_date,
                                    MAX(create_time) AS last_create_time
                             FROM ycx_children_leave_record
                             WHERE child_id = #{childId}
                               AND DATE_FORMAT(leave_date, '%Y-%m') = #{month}
                             GROUP BY child_id, leave_date) AS last_records
                                ON r1.child_id = last_records.child_id
                                AND r1.leave_date = last_records.leave_date
                                AND r1.create_time = last_records.last_create_time
        WHERE r1.status = 1;
    </select>
</mapper>