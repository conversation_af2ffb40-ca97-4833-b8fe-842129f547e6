<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.inma.mapper.YcxInmaMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.inma.model.YcxInma">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.inma.model.YcxInmaOption">
      <result column="inma_id" property="inmaId"/>
      <result column="name" property="name"/>
      <result column="value" property="value"/>
      <result column="score" property="score"/>
    </collection>
  </resultMap>

  <select id="getList" resultMap="BaseResultMap">
    select inma.*
    , op.inma_id
    , op.name
    , op.value
    , op.score
    from ycx_inma as inma
    inner join ycx_inma_option as op
    on op.inma_id = inma.id
    where  inma.status = 1
    order by inma.order_num asc, op.id asc
  </select>
</mapper>