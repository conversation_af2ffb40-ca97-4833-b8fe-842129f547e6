package com.bmh.project.evaluation.ygtss.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.ygtss.model.YcxYgtssSelected;

import java.util.List;

/**
 * ycx_ygtss_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月13日 13:48:54
 */
public interface YcxYgtssSelectedService extends BaseService<YcxYgtssSelected> {

    /**
     * 保存选择项
     *
     * @param selecteds
     * @param recordId
     */
    void saveSelected(List<YcxYgtssSelected> selecteds, Integer recordId);

    /**
     * 获取评测结果
     *
     * @param recordId
     * @return
     */
    List<YcxYgtssSelected> getSelected(Integer recordId);
}