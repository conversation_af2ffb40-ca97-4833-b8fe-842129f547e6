package com.bmh.common.security.service;

import cn.hutool.core.lang.UUID;
import com.bmh.common.constant.Constants;
import com.bmh.common.redis.RedisService;
import com.bmh.common.security.model.LoginUser;
import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * token验证处理
 *
 * <AUTHOR>
 */
@Component
public class TokenService {

    private static final long MILLIS_SECOND = 1000;
    private static final long MILLIS_MINUTE = 60 * MILLIS_SECOND;
    private static final Long MILLIS_MINUTE_TEN = 20 * 60 * 1000L;
    private static final Long MILLIS_MINUTE_DAY = 24 * 60 * 60 * 1000L;
    @Value ("${token.header}")
    private String header;
    @Value ("${token.secret}")
    private String secret;
    @Value ("${token.expireTime}")
    private long expireTime;
    @Resource
    private RedisService<String, Object> redisService;

    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser (HttpServletRequest request) {
        // 获取请求携带的令牌
        String jwtToken = getJwtToken (request);
        return getLoginUser (jwtToken);
    }


    /**
     * 获取用户身份信息
     *
     * @return 用户信息
     */
    public LoginUser getLoginUser (String jwtToken) {
        // 获取请求携带的令牌
        if (StringUtils.isNotEmpty (jwtToken)) {
            Claims claims = parseJwtToken (jwtToken);
            // 解析对应的权限以及用户信息
            String redisKey = Constants.REDIS_LOGIN_TOKEN_KEY
                    + claims.get (Constants.JWT_USERID)
                    + ":" + claims.get (Constants.JWT_LOGIN_USER_KEY);
            return (LoginUser) redisService.get (redisKey);
        }
        return null;
    }

    /**
     * 设置用户身份信息
     */
    public void setLoginUser (LoginUser loginUser) {
        if (Objects.nonNull (loginUser) && StringUtils.isNotEmpty (loginUser.getRedisKey ())) {
            refreshRedisToken (loginUser);
        }
    }

    /**
     * 删除用户身份信息
     */
    public void delLoginUser (String redisKey) {
        if (StringUtils.isNotEmpty (redisKey)) {
            redisService.remove (Constants.REDIS_LOGIN_TOKEN_KEY + redisKey);
        }
    }

    /**
     * 删除指定用户身份信息
     */
    public void delLoginUser (Integer userId) {
        redisService.removeBlear (Constants.REDIS_LOGIN_TOKEN_KEY + userId + ":*");
    }

    /**
     * 创建令牌
     *
     * @param loginUser 用户信息
     * @return 令牌
     */
    public String createJwtToken (LoginUser loginUser) {
        // TODO：多次登录时redisKey可复用，无需每次重新生成
        String uuid = UUID.fastUUID ().toString ();
        loginUser.setRedisKey (loginUser.getUserId () + ":" + uuid);
        refreshRedisToken (loginUser);

        Map<String, Object> claims = new HashMap<> ();
        claims.put (Constants.JWT_LOGIN_USER_KEY, uuid);
        claims.put (Constants.JWT_USERID, loginUser.getUserId ());
        return createJwtToken (claims);
    }

    /**
     * 验证令牌有效期，相差不足20分钟，自动刷新缓存
     *
     * @param loginUser 登录用户
     * @return 令牌
     */
    public void verifyToken (LoginUser loginUser) {
        long expireTime = loginUser.getExpireTime ();
        long currentTime = System.currentTimeMillis ();
        if (expireTime - currentTime <= MILLIS_MINUTE_DAY) {
            refreshRedisToken (loginUser);
        }
    }

    /**
     * 刷新令牌有效期
     *
     * @param loginUser 登录信息
     */
    public void refreshRedisToken (LoginUser loginUser) {
        loginUser.setLoginTime (System.currentTimeMillis ());
        loginUser.setExpireTime (loginUser.getLoginTime () + expireTime);
        // 根据uuid将loginUser缓存
        // redis key格式：
        // login_tokens:userId:用户登录时生成的UUID
        String redisKey = Constants.REDIS_LOGIN_TOKEN_KEY + loginUser.getRedisKey ();
        redisService.set (redisKey, loginUser, expireTime);
    }

    /**
     * 从数据声明生成令牌
     *
     * @param claims 数据声明
     * @return 令牌
     */
    private String createJwtToken (Map<String, Object> claims) {
        return Jwts.builder ()
                .setClaims (claims)
                .signWith (SignatureAlgorithm.HS512, secret).compact ();
    }

    /**
     * 从令牌中获取数据声明
     *
     * @param token 令牌
     * @return 数据声明
     */
    private Claims parseJwtToken (String token) {
        return Jwts.parser ()
                .setSigningKey (secret)
                .parseClaimsJws (token)
                .getBody ();
    }

    /**
     * 获取请求token
     *
     * @param request
     * @return token
     */
    private String getJwtToken (HttpServletRequest request) {
        String token = request.getHeader (header);
        if (StringUtils.isNotEmpty (token) && token.startsWith (Constants.JWT_TOKEN_PREFIX)) {
            token = token.replace (Constants.JWT_TOKEN_PREFIX, "");
        }
        return token;
    }

}
