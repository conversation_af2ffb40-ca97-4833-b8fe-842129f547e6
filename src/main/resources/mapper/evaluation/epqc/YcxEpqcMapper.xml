<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.epqc.mapper.YcxEpqcMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.epqc.model.YcxEpqc">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.epqc.model.YcxEpqcOption" >
      <result column="epqc_id" jdbcType="INTEGER" property="epqcId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="value" jdbcType="VARCHAR" property="value" />
      <result column="score" jdbcType="VARCHAR" property="score" />
    </collection>

  </resultMap>
    <select id="getEpqcByType" resultMap="BaseResultMap">
      select epqc.*
    , op.epqc_id
    , op.name
    , op.value
    , op.score
    from ycx_epqc as epqc
    inner join ycx_epqc_option as op
    on op.epqc_id = epqc.id
    and epqc.status = 1
    order by epqc.order_num asc, op.id asc
    </select>
</mapper>