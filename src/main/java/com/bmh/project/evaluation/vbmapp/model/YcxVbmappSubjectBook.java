package com.bmh.project.evaluation.vbmapp.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_vbmapp_subject_book")
public class YcxVbmappSubjectBook implements Serializable {
    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 题目Id
     */
    @Column(name = "subject_id")
    private Integer subjectId;

    /**
     * 标签
     */
    @Column(name = "label")
    private String label;

    /**
     * 内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 排序号
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 类型（1项目，2分数）
     */
    @Column(name = "type")
    private Integer type;

    private static final long serialVersionUID = 1L;
}