<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ea.mapper.EaCourseMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ea.model.EaCourse">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="teacher_id" jdbcType="INTEGER" property="teacherId" />
    <result column="teacher_name" jdbcType="VARCHAR" property="teacherName" />
    <result column="child_flow_id" jdbcType="INTEGER" property="childFlowId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="erp_task_id" jdbcType="INTEGER" property="erpTaskId" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="getMonthDate" resultType="java.lang.String">
    select DATE_FORMAT(start_time,'%Y-%m-%d')
    from ea_course
    where child_id = #{childId}
      and DATE_FORMAT(start_time, '%Y-%m') = #{month}
    group by start_time
    order by start_time desc
  </select>
    <select id="getReportCourseInfo" resultType="com.bmh.project.report.vo.EaCourseVo">
      SELECT
        ec.start_time startTime,
        ec.end_time endTime,
        ecfn.`name` flowName,
        ecfsn.full_name nodeName,
        ecfsn.standard,
        ecr.target_done targetDone,
        ecr.problem_behavior problemBehavior,
        ecr.problem_reason problemReason
      FROM
        ea_course ec
          LEFT JOIN ea_child_flow ecf ON ec.child_flow_id = ecf.id
          LEFT JOIN ea_child_flow_node ecfn ON ec.child_flow_id = ecfn.child_flow_id
          LEFT JOIN ea_child_flow_sub_node ecfsn ON ecf.id = ecfsn.child_flow_id
          AND ecfn.id = ecfsn.child_flow_node_id
          LEFT JOIN ea_course_record ecr ON ec.id = ecr.course_id
          AND ecf.id = ecr.child_flow_id
          AND ecfn.id = ecr.child_flow_node_id
          AND ecfsn.id = ecr.child_flow_sub_node_id
      WHERE
        ec.child_id = #{childrenId}
        AND ec.`status` = 2
        AND ecf.`status` = 1
        AND ecfn.`status` = 1
        AND ecfsn.`status` =1
        AND ec.end_time BETWEEN #{startTime} AND #{endTime}
    </select>
</mapper>