<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ev.mapper.EvPepDomainMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ev.model.EvPepDomain">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="quiz_domain" jdbcType="VARCHAR" property="quizDomain" />
    <result column="synthesis_domain" jdbcType="VARCHAR" property="synthesisDomain" />
    <result column="power_domain" jdbcType="VARCHAR" property="powerDomain" />
    <result column="short_power_domain" jdbcType="VARCHAR" property="shortPowerDomain" />
    <result column="is_cal_dev_age" jdbcType="INTEGER" property="isCalDevAge" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>
</mapper>
