<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaPrepProjectGoalMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaPrepProjectGoal">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="prep_id" jdbcType="INTEGER" property="prepId" />
    <result column="prep_project_id" jdbcType="INTEGER" property="prepProjectId" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="plan_project_id" jdbcType="INTEGER" property="planProjectId" />
    <result column="plan_project_goal_id" jdbcType="INTEGER" property="planProjectGoalId" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="short_goal_id" jdbcType="INTEGER" property="shortGoalId" />
    <result column="short_goal_name" jdbcType="VARCHAR" property="shortGoalName" />
    <result column="assist_method" jdbcType="VARCHAR" property="assistMethod" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="target" jdbcType="LONGVARCHAR" property="target" />
  </resultMap>
</mapper>