<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.abc.mapper.YcxAbcSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.abc.model.YcxAbcSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="abc_id" jdbcType="INTEGER" property="abcId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />


    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="optionName" jdbcType="VARCHAR" property="optionName" />

  </resultMap>


<!--  List<YcxAbcSelected> getSelecteds(Integer recordId);-->
  <select id="getSelecteds" parameterType="Integer" resultMap="BaseResultMap" >
    select sele.*
    , abc.content
    , abc.order_num
    , op.name as optionName
    from ycx_abc_selected as sele
    inner join ycx_abc as abc
    on abc.id = sele.abc_id
    left join ycx_abc_option as op
    on op.abc_id = abc.id
    and op.value = sele.answer
    where sele.record_id = #{recordId}
    order by abc.order_num asc
  </select>

</mapper>