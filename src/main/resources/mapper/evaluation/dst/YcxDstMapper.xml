<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.dst.mapper.YcxDstMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.dst.model.YcxDst">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="title" jdbcType="VARCHAR" property="title"/>
        <result column="tips" jdbcType="VARCHAR" property="tips"/>
        <result column="grading" jdbcType="VARCHAR" property="grading"/>
        <result column="month" jdbcType="INTEGER" property="month"/>
        <result column="is_blank" jdbcType="INTEGER" property="isBlank"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    </resultMap>
</mapper>