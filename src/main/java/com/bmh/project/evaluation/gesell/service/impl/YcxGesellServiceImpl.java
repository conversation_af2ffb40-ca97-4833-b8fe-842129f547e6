package com.bmh.project.evaluation.gesell.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.gesell.mapper.YcxGesellMapper;
import com.bmh.project.evaluation.gesell.model.YcxGesell;
import com.bmh.project.evaluation.gesell.service.YcxGesellService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxGesellService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月09日 09:09:25
 */
@Service
public class YcxGesellServiceImpl extends BaseServiceImpl<YcxGesell> implements YcxGesellService {
    @Resource
    private YcxGesellMapper ycxGesellMapper;

    @Override
    public List<YcxGesell> getGeSellByType(Integer type) {
        return ycxGesellMapper.getGeSellByType(type);
    }


}