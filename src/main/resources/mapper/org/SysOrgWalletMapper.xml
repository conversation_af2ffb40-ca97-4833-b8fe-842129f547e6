<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.org.mapper.SysOrgWalletMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.org.model.SysOrgWallet">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="recharge_amount" jdbcType="DECIMAL" property="rechargeAmount" />
    <result column="consum_amount" jdbcType="DECIMAL" property="consumAmount" />
    <result column="balance" jdbcType="DECIMAL" property="balance" />
    <result column="withdrawal_amount" jdbcType="DECIMAL" property="withdrawalAmount" />
    <result column="invoice_amount" jdbcType="DECIMAL" property="invoiceAmount" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectByOrgId" resultMap="BaseResultMap">
    select * from sys_org_wallet where org_id = #{orgId}
  </select>
</mapper>