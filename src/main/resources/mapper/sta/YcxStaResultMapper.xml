<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.sta.mapper.YcxStaResultMapper">

    <resultMap id="YcxStaResultMap" type="com.bmh.project.evaluation.sta.model.YcxStaResult">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="recordId" column="record_id" jdbcType="INTEGER"/>
        <result property="childrenId" column="children_id" jdbcType="INTEGER"/>
        <result property="childrenAge" column="children_age" jdbcType="VARCHAR"/>
        <result property="orgId" column="org_id" jdbcType="INTEGER"/>
        <result property="result" column="result" jdbcType="VARCHAR"/>
        <result property="recom" column="recom" jdbcType="VARCHAR"/>
        <result property="resultHis" column="result_his" jdbcType="VARCHAR"/>
        <result property="resultChart" column="result_chart" jdbcType="VARCHAR"/>
        <result property="operatorId" column="operator_id" jdbcType="INTEGER"/>
        <result property="operatorName" column="operator_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <select id="selectByRecordId" resultType="com.bmh.project.evaluation.sta.vo.StaSelectedVo">
        SELECT
            ys.id,
            ys.type,
            ys.tag,
            yss.answer,
            yss.score
        FROM
            ycx_sta_selected yss
                INNER JOIN ycx_sta ys ON yss.sta_id = ys.id
        WHERE
            yss.record_id = #{recordId} AND yss.`status` = 1
    </select>

</mapper>

