package com.bmh.project.evaluation.weiss.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.weiss.model.YcxWeissSelected;

import java.util.List;
import java.util.Map;

/**
 * ycx_weiss_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年07月10日 16:42:05
 */
public interface YcxWeissSelectedService extends BaseService<YcxWeissSelected> {

    /**
     * 保存选择项
     * @param selecteds 选择项
     * @param recordId 记录ID
     */
    void saveSelecteds(List<YcxWeissSelected> selecteds, Integer recordId);

    /**
     * 获取选择项(按类型分类)
     * @param recordId 记录ID
     */
    Map<Integer, List<YcxWeissSelected>> getSelecteds(Integer recordId);
}