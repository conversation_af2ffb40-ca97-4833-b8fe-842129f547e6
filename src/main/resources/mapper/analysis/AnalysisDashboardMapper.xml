<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bmh.project.analysis.mapper.AnalysisDashboardMapper">
    <sql id="COMMON_CONDITION">
        <if test="orgId != null">
            AND org_id = #{orgId}
        </if>
        <choose>
            <when test="precondition != null and precondition == 1">
                AND DATE(create_time) = CURRENT_DATE
            </when>
            <when test="precondition != null and precondition == 2">
                AND DATE(create_time) = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)
            </when>
            <when test="precondition != null and precondition == 3">
                AND YEARWEEK(create_time, 1) = YEARWEEK(NOW(), 1)
            </when>
            <when test="precondition != null and precondition == 4">
                AND DATE (create_time) >= DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY)
            </when>
        </choose>
        <if test="queryStartDate != null">
            AND DATE (create_time) >= DATE (
                #{queryStartDate})
        </if>
        <if test="queryEndDate != null">
            AND DATE(create_time) &lt;= DATE(#{queryEndDate})
        </if>
    </sql>

    <sql id="VIEW_COURSE_CONDITION">
        <if test="orgId != null">
            AND org_id = #{orgId}
        </if>
        <choose>
            <when test="precondition != null and precondition == 1">
                AND DATE(end_time) = CURRENT_DATE
            </when>
            <when test="precondition != null and precondition == 2">
                AND DATE(end_time) = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)
            </when>
            <when test="precondition != null and precondition == 3">
                AND YEARWEEK(end_time, 1) = YEARWEEK(NOW(), 1)
            </when>
            <when test="precondition != null and precondition == 4">
                AND DATE(end_time) >= DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY)
            </when>
        </choose>
        <if test="queryStartDate != null">
            AND DATE(end_time) >= DATE(#{queryStartDate})
        </if>
        <if test="queryEndDate != null">
            AND DATE(end_time) &lt;= DATE(#{queryEndDate})
        </if>
    </sql>

    <select id="getCourseCount" resultType="integer">
        SELECT COUNT(*)
        FROM view_analysis_course
        <where>
           <include refid="VIEW_COURSE_CONDITION"/>
        </where>
    </select>

    <select id="getTeacherCount" resultType="integer">
        SELECT COUNT(DISTINCT teacher_id)
        FROM view_analysis_course
        <where>
            <include refid="VIEW_COURSE_CONDITION"/>
        </where>
    </select>

    <select id="getChildIds" resultType="java.lang.String">
        select DISTINCT child_ids
        from view_analysis_course
        <where>
            <include refid="VIEW_COURSE_CONDITION"/>
        </where>
    </select>

    <select id="getChildCount" resultType="integer">
        SELECT COUNT(id)
        FROM ycx_children
        <where>
            <include refid="VIEW_COURSE_CONDITION"/>
        </where>
    </select>

    <select id="getSupervisionData" resultType="map">
        SELECT SUM(supervisionProjectCount) AS supervisionProjectCount,
               SUM(supervisionCount)        AS supervisionCount
        FROM
        (
        SELECT COUNT(DISTINCT apph.id)                     AS supervisionProjectCount,
               COUNT(DISTINCT apph.plan_id, apph.batch_no) AS supervisionCount
        FROM aba_plan_project_history apph
                 LEFT JOIN aba_plan ap ON apph.plan_id = ap.id
        <where>
            ap.`status` = 1
            <if test="orgId != null">
                AND ap.org_id = #{orgId}
            </if>
            <choose>
                <when test="precondition != null and precondition == 1">
                    AND DATE(apph.create_time) = CURRENT_DATE
                </when>
                <when test="precondition != null and precondition == 2">
                    AND DATE(apph.create_time) = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)
                </when>
                <when test="precondition != null and precondition == 3">
                    AND YEARWEEK(apph.create_time, 1) = YEARWEEK(NOW(), 1)
                </when>
                <when test="precondition != null and precondition == 4">
                    AND DATE(apph.create_time) >= DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY)
                </when>
            </choose>
            <if test="queryStartDate != null">
                AND DATE(apph.create_time) >= DATE(#{queryStartDate})
            </if>
            <if test="queryEndDate != null">
                AND DATE(apph.create_time) &lt;= DATE(#{queryEndDate})
            </if>
        </where>
<!--   2024/03/27 去除言语统计     -->
<!--        UNION ALL-->
<!--        SELECT COUNT(DISTINCT spah.id)                     AS supervisionProjectCount,-->
<!--               COUNT(DISTINCT spah.plan_id, spah.batch_no) AS supervisionCount-->
<!--        FROM st_plan_activity_history spah-->
<!--                 LEFT JOIN st_plan sp ON spah.plan_id = sp.id-->
<!--        <where>-->
<!--            <if test="orgId != null">-->
<!--                AND sp.org_id = #{orgId}-->
<!--            </if>-->
<!--            <choose>-->
<!--                <when test="precondition != null and precondition == 1">-->
<!--                    AND DATE(spah.create_time) = CURRENT_DATE-->
<!--                </when>-->
<!--                <when test="precondition != null and precondition == 2">-->
<!--                    AND DATE(spah.create_time) = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)-->
<!--                </when>-->
<!--                <when test="precondition != null and precondition == 3">-->
<!--                    AND YEARWEEK(spah.create_time, 1) = YEARWEEK(NOW(), 1)-->
<!--                </when>-->
<!--                <when test="precondition != null and precondition == 4">-->
<!--                    AND DATE(spah.create_time) >= DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY)-->
<!--                </when>-->
<!--            </choose>-->
<!--            <if test="queryStartDate != null">-->
<!--                AND DATE(spah.create_time) >= DATE(#{queryStartDate})-->
<!--            </if>-->
<!--            <if test="queryEndDate != null">-->
<!--                AND DATE(spah.create_time) &lt;= DATE(#{queryEndDate})-->
<!--            </if>-->
<!--        </where>-->
        ) supervision;
    </select>

    <select id="getSupervisionChildCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT apph.plan_id) AS supervisionChildCount
        FROM aba_plan ap
                 LEFT JOIN aba_plan_project_history apph ON apph.plan_id = ap.id
        WHERE ap.`status` = 1
        <if test="orgId != null">
            AND ap.org_id = #{orgId}
        </if>
        <choose>
            <when test="precondition != null and precondition == 1">
                AND DATE(apph.create_time) = CURRENT_DATE
            </when>
            <when test="precondition != null and precondition == 2">
                AND DATE(apph.create_time) = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)
            </when>
            <when test="precondition != null and precondition == 3">
                AND YEARWEEK(apph.create_time, 1) = YEARWEEK(NOW(), 1)
            </when>
            <when test="precondition != null and precondition == 4">
                AND DATE(apph.create_time) >= DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY)
            </when>
        </choose>
        <if test="queryStartDate != null">
            AND DATE(apph.create_time) >= DATE(
                    #{queryStartDate})
        </if>
        <if test="queryEndDate != null">
            AND DATE(apph.create_time) &lt;= DATE(#{queryEndDate})
        </if>
    </select>

    <select id="getAbnormalSupervisionCount" resultType="java.lang.Integer">
        SELECT SUM(aas.abnormalChildCount)
        FROM (SELECT org_id, abnormal_date, count(DISTINCT child_id) AS abnormalChildCount
              FROM aba_abnormal_supervision
              GROUP BY org_id, abnormal_date) aas
        <where>
            <if test="orgId != null">
                AND aas.org_id = #{orgId}
            </if>
            <choose>
                <when test="precondition != null and precondition == 1">
                    AND aas.abnormal_date = CURRENT_DATE
                </when>
                <when test="precondition != null and precondition == 2">
                    AND aas.abnormal_date = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)
                </when>
                <when test="precondition != null and precondition == 3">
                    AND YEARWEEK(aas.abnormal_date, 1) = YEARWEEK(NOW(), 1)
                </when>
                <when test="precondition != null and precondition == 4">
                    AND aas.abnormal_date >= DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY)
                </when>
            </choose>
            <if test="queryStartDate != null">
                AND aas.abnormal_date >= DATE(#{queryStartDate})
            </if>
            <if test="queryEndDate != null">
                AND aas.abnormal_date &lt;= DATE(#{queryEndDate})
            </if>
        </where>
    </select>
    <select id="getThirtyDayCourse" resultType="com.bmh.project.dashboard.vo.DailyCourseVo">
        SELECT
        DATE(start_time) AS classDate,
        COUNT(course_id) AS classCount
        FROM
        view_analysis_course
        WHERE
        start_time &gt; #{startTime}
        AND start_time &lt; #{endTime}
        <if test="orgId!=0">
            AND org_id = #{orgId}
        </if>
        <if test="0==orgId">
            AND org_id NOT IN (1,19,43,65)
        </if>
        GROUP BY
        DATE(start_time)
        ORDER BY
        classDate ASC
    </select>

    <select id="getTeacherCourseCount" resultType="com.bmh.project.dashboard.vo.TeacherRankVo">
        SELECT teacher_name AS teachName,
               teacher_id AS teachId,
               COUNT(course_id) AS classCount
        FROM view_analysis_course
        WHERE start_time &gt; #{startTime}
          AND start_time &lt; #{endTime}
          AND org_id = #{orgId}
        GROUP BY teacher_id
        ORDER BY classCount DESC
        LIMIT 10;
    </select>
    <select id="getAllOrgTeacherCourseCount" resultType="com.bmh.project.dashboard.vo.TeacherRankVo">
        SELECT org_short_name AS teachName,
               org_id AS teachId,
               COUNT(course_id) AS classCount
        FROM view_analysis_course
        WHERE start_time &gt; #{startTime}
          AND start_time &lt; #{endTime}
          AND org_id NOT IN ( 1, 19, 43, 65 )
        GROUP BY org_id
        ORDER BY classCount DESC
    </select>
    <select id="getDashBoardCourseCount" resultType="java.lang.Integer">
        SELECT COUNT(course_id)
        FROM view_analysis_course
        <where>
            <include refid="VIEW_COURSE_CONDITION_DASHBOARD"/>
        </where>
    </select>
    <select id="getDashBoardTeacherCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT teacher_id)
        FROM view_analysis_course
        <where>
            <include refid="VIEW_COURSE_CONDITION_DASHBOARD"/>
        </where>
    </select>
    <select id="getDashBoardChildIds" resultType="java.lang.String">
        select DISTINCT child_ids
        from view_analysis_course
        <where>
            <include refid="VIEW_COURSE_CONDITION_DASHBOARD"/>
        </where>
    </select>
    <select id="getDashBoardSupervisionData" resultType="java.util.Map">
        SELECT SUM(supervisionProjectCount) AS supervisionProjectCount,
        SUM(supervisionCount)        AS supervisionCount
        FROM
        (
        SELECT COUNT(DISTINCT apph.id)                     AS supervisionProjectCount,
        COUNT(DISTINCT apph.plan_id, apph.batch_no) AS supervisionCount
        FROM aba_plan_project_history apph
        LEFT JOIN aba_plan ap ON apph.plan_id = ap.id
        <where>
            ap.`status` = 1
            <if test="orgId != null and orgId != 0">
                AND ap.org_id = #{orgId}
            </if>
            <if test="0==orgId">
                AND ap.org_id NOT IN (1,19,43,65)
            </if>
            <choose>
                <when test="precondition != null and precondition == 1">
                    AND DATE(apph.create_time) = CURRENT_DATE
                </when>
                <when test="precondition != null and precondition == 2">
                    AND DATE(apph.create_time) = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)
                </when>
                <when test="precondition != null and precondition == 3">
                    AND YEARWEEK(apph.create_time, 1) = YEARWEEK(NOW(), 1)
                </when>
                <when test="precondition != null and precondition == 4">
                    AND DATE(apph.create_time) >= DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY)
                </when>
            </choose>
            <if test="queryStartDate != null">
                AND DATE(apph.create_time) >= DATE(#{queryStartDate})
            </if>
            <if test="queryEndDate != null">
                AND DATE(apph.create_time) &lt;= DATE(#{queryEndDate})
            </if>
        </where>
        <!--   2024/03/27 去除言语统计     -->
        <!--        UNION ALL-->
        <!--        SELECT COUNT(DISTINCT spah.id)                     AS supervisionProjectCount,-->
        <!--               COUNT(DISTINCT spah.plan_id, spah.batch_no) AS supervisionCount-->
        <!--        FROM st_plan_activity_history spah-->
        <!--                 LEFT JOIN st_plan sp ON spah.plan_id = sp.id-->
        <!--        <where>-->
        <!--            <if test="orgId != null">-->
        <!--                AND sp.org_id = #{orgId}-->
        <!--            </if>-->
        <!--            <choose>-->
        <!--                <when test="precondition != null and precondition == 1">-->
        <!--                    AND DATE(spah.create_time) = CURRENT_DATE-->
        <!--                </when>-->
        <!--                <when test="precondition != null and precondition == 2">-->
        <!--                    AND DATE(spah.create_time) = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)-->
        <!--                </when>-->
        <!--                <when test="precondition != null and precondition == 3">-->
        <!--                    AND YEARWEEK(spah.create_time, 1) = YEARWEEK(NOW(), 1)-->
        <!--                </when>-->
        <!--                <when test="precondition != null and precondition == 4">-->
        <!--                    AND DATE(spah.create_time) >= DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY)-->
        <!--                </when>-->
        <!--            </choose>-->
        <!--            <if test="queryStartDate != null">-->
        <!--                AND DATE(spah.create_time) >= DATE(#{queryStartDate})-->
        <!--            </if>-->
        <!--            <if test="queryEndDate != null">-->
        <!--                AND DATE(spah.create_time) &lt;= DATE(#{queryEndDate})-->
        <!--            </if>-->
        <!--        </where>-->
        ) supervision;
    </select>

    <sql id="VIEW_COURSE_CONDITION_DASHBOARD">
        <if test="orgId != null and orgId != 0">
            AND org_id = #{orgId}
        </if>
        <if test="0==orgId">
            AND org_id NOT IN (1,19,43,65)
        </if>
        <choose>
            <when test="precondition != null and precondition == 1">
                AND DATE(end_time) = CURRENT_DATE
            </when>
            <when test="precondition != null and precondition == 2">
                AND DATE(end_time) = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)
            </when>
            <when test="precondition != null and precondition == 3">
                AND YEARWEEK(end_time, 1) = YEARWEEK(NOW(), 1)
            </when>
            <when test="precondition != null and precondition == 4">
                AND DATE(end_time) >= DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY)
            </when>
        </choose>
        <if test="queryStartDate != null">
            AND DATE(end_time) >= DATE(#{queryStartDate})
        </if>
        <if test="queryEndDate != null">
            AND DATE(end_time) &lt;= DATE(#{queryEndDate})
        </if>
    </sql>

    <select id="getAllOrgChildCount" resultType="com.bmh.project.dashboard.vo.OrgChildCountVo">
        SELECT
            org_id AS orgId,
            COUNT(distinct child_ids) AS childCount
        FROM view_analysis_course AS a
        WHERE
            start_time &gt; #{startTime}
            AND start_time &lt; #{endTime}
            AND org_id NOT IN (1,19,43,65)
            AND course_type IN (1,2)
        GROUP BY org_id
    </select>
</mapper>
