package com.bmh.project.evaluation.rutterp.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.rutterp.mapper.YcxRutterParentSelectedMapper;
import com.bmh.project.evaluation.rutterp.model.YcxRutterParentSelected;
import com.bmh.project.evaluation.rutterp.service.YcxRutterParentSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxRutterParentSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月16日 18:13:51
 */
@Service
public class YcxRutterParentSelectedServiceImpl extends BaseServiceImpl<YcxRutterParentSelected> implements YcxRutterParentSelectedService {
    @Resource
    private YcxRutterParentSelectedMapper ycxRutterParentSelectedMapper;

    @Override
    public void saveSelected(List<YcxRutterParentSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreatedTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        insertList(selecteds);
    }

    @Override
    public List<YcxRutterParentSelected> getSelecteds(Integer recordId) {
        return ycxRutterParentSelectedMapper.getSelecteds(recordId);
    }
}