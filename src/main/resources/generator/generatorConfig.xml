<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>

    <properties resource="application-dev.properties"/>

    <context id="Mysql" targetRuntime="MyBatis3Simple" defaultModelType="flat">
        <property name="javaFileEncoding" value="UTF-8"/>
        <!-- 生成tk.mapper专用mapper文件 -->
        <plugin type="tk.mybatis.mapper.generator.MapperPlugin">
            <property name="mappers" value="com.bmh.common.base.BaseMapper"/>
            <property name="caseSensitive" value="true"/>
            <property name="forceAnnotation" value="true"/>
            <property name="beginningDelimiter" value=""/>
            <property name="endingDelimiter" value=""/>
            <property name="lombok" value="Data"/>
        </plugin>
        <!-- 生成service文件 -->
        <plugin type="com.bmh.common.generator.GenerateServicePlugin">
            <property name="baseServices" value="com.bmh.common.base.BaseService"/>
            <property name="baseServiceImpls" value="com.bmh.common.base.BaseServiceImpl"/>
            <property name="targetProject" value="./src/main/java"/>
        </plugin>
        <!-- 序列化插件 -->
        <plugin type="org.mybatis.generator.plugins.SerializablePlugin"/>

        <!-- 数据库连接 -->
        <jdbcConnection driverClass="${spring.datasource.driver-class-name}"
                        connectionURL="${spring.datasource.url}"
                        userId="${spring.datasource.username}"
                        password="${spring.datasource.password}">
            <property name="nullCatalogMeansCurrent" value="true"/>
        </jdbcConnection>

        <!-- model对象生成位置 -->
        <javaModelGenerator targetPackage="com.bmh.project.ev.model" targetProject="src/main/java"/>
        <!-- mapper.xml文件生成位置 -->
        <sqlMapGenerator targetPackage="mapper.ev" targetProject="src/main/resources"/>
        <!-- mapper对象生成位置 -->
        <javaClientGenerator targetPackage="com.bmh.project.ev.mapper" targetProject="src/main/java"
                             type="XMLMAPPER"/>
        <!--所有表生成 -->
<!--        <table schema="project" tableName="st_%">-->
<!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
<!--        </table>-->

        <!-- 单独生成单个表 -->
        <!--        <table schema="ycx" tableName="aba_homework">-->
        <!--            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>-->
        <!--        </table>-->
        <table schema="ycx" tableName="ev_pep_domain">
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
        </table>
        <table schema="ycx" tableName="ev_pep_project">
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
        </table>
        <table schema="ycx" tableName="ev_pep_proejct_option">
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
        </table>
        <table schema="ycx" tableName="ev_pep_project_result">
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
        </table>
        <table schema="ycx" tableName="ev_pep_power_domain_standard">
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
        </table>
        <table schema="ycx" tableName="ev_pep_power_domain_result">
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
        </table>
        <table schema="ycx" tableName="ev_pep_synthesis_domian_standard">
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
        </table>
        <table schema="ycx" tableName="ev_pep_synthesis_domian_result">
            <generatedKey column="id" sqlStatement="Mysql" identity="true"/>
        </table>


    </context>
</generatorConfiguration>
