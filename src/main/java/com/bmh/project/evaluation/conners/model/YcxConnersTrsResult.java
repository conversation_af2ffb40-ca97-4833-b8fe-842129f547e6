package com.bmh.project.evaluation.conners.model;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Table(name = "ycx_conners_trs_result")
public class YcxConnersTrsResult extends YcxResult implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 孩子年龄(周)
     */
    @Column(name = "children_age")
    private BigDecimal childrenAge;

    /**
     * 孩子ID
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 单位ID
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 品行问题结果
     */
    @Column(name = "res_type1")
    private String resType1;

    /**
     * 学习问题结果
     */
    @Column(name = "res_type2")
    private String resType2;

    /**
     * 心身问题结果
     */
    @Column(name = "res_type3")
    private String resType3;

    /**
     * 冲动-多动结果
     */
    @Column(name = "res_type4")
    private String resType4;

    /**
     * 焦虑结果
     */
    @Column(name = "res_type5")
    private String resType5;

    /**
     * 建议
     */
    @Column(name = "recom")
    private String recom;

    /**
     * 筛查结果
     */
    @Column(name = "result")
    private String result;

    /**
     * 用于展示历史档案的字符串
     */
    @Column(name = "result_his")
    private String resultHis;

    /**
     * 操作人ID
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 选择项
     */
    @Transient
    private List<YcxConnersTrsSelected> selecteds;

    private static final long serialVersionUID = 1L;
}