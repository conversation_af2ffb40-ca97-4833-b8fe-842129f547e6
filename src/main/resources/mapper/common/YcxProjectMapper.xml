<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.common.mapper.YcxProjectMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.common.model.YcxProject">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="classify" jdbcType="INTEGER" property="classify" />
    <result column="tag" jdbcType="VARCHAR" property="tag" />
    <result column="short_title" jdbcType="VARCHAR" property="shortTitle" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="min_month" jdbcType="INTEGER" property="minMonth" />
    <result column="max_month" jdbcType="INTEGER" property="maxMonth" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <update id="setProjectImg">
    update ${tableName} set ${column} = imgUrl where id = #{projectId}
  </update>
</mapper>