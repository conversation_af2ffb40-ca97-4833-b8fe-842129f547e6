package com.bmh.project.evaluation.weiss.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.weiss.model.YcxWeiss;
import com.bmh.project.evaluation.weiss.service.YcxWeissService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Weiss功能缺陷量表
 *
 * <AUTHOR>
 * @since 2021/7/10 16:42
 */
@RestController
@RequestMapping("/weiss")
public class YcxWeissController {

    @Resource
    private YcxWeissService weissService;

    /**
     * 根据类型获取问题
     * @param type 类型(1家庭 2学习和学校 3生活技能 4自我管理 5社会活动 6冒险活动)
     * @return
     */
    @RequestMapping("/getWeissListByType")
    public Result<List<YcxWeiss>> getWeissListByType(Integer type){
        List<YcxWeiss> list = weissService.getWeissListByType (type);
        return ResultUtil.success(list);
    }

}
