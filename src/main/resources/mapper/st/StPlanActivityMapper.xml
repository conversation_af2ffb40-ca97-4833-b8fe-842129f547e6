<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.st.mapper.StPlanActivityMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.st.model.StPlanActivity">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="level_id" jdbcType="INTEGER" property="levelId" />
    <result column="level_name" jdbcType="VARCHAR" property="levelName" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="activity_id" jdbcType="INTEGER" property="activityId" />
    <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="opt_type" jdbcType="INTEGER" property="optType" />
    <result column="status" jdbcType="INTEGER" property="status" />
      <result column="badge" jdbcType="INTEGER" property="badge" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>


  <select id="getProjectListByPlanId" resultType="com.bmh.project.st.vo.StPlanProjectVo">
      select plan_id    as planId,
             level_id   as levelId,
             level_name as levelName,
             domain_id  as domainId,
             domain_name as domainName,
             project_id as projectId,
             project_name as projectName,
             activity_id as activityId,
             activity_name as activityName
      from st_plan_activity
      where plan_id = #{planId}
        and status = 1
      group by project_id
      order by level_id, domain_id, project_id
  </select>

  <select id="getActivityDetailList" resultType="com.bmh.project.st.vo.StPlanActivityDetailVo">
      select spa.id,
             spa.org_id             as orgId,
             spa.child_id           as childId,
             spa.plan_id            as planId,
             spa.activity_id        as activityId,
             spa.activity_name      as activityName,
             sa.scope               as scope,
             sa.teach_aid           as teachAid,
             sa.operate_video       as operateVideo
      from st_plan_activity spa
               left join st_activity sa on spa.activity_id = sa.id
      where spa.plan_id = #{planId}
        and spa.project_id = #{projectId}
        and spa.status = 1
      order by spa.activity_id
  </select>

  <select id="getStPassRankList" resultType="com.bmh.project.analysis.vo.AnalysisChildPassRankVo">
      SELECT
      spa.org_id AS orgId,
      spa.child_id AS childId,
      yc.`name` AS childName,
      COUNT(spa.id) AS passCount
      FROM
      `st_plan_activity` spa
      LEFT JOIN ycx_children yc ON spa.child_id = yc.id
      WHERE
      spa.`status` = 2
      <if test="orgId != null">
          AND spa.org_id = #{orgId}
      </if>
      <choose>
          <when test="precondition != null and precondition == 1">
              AND DATE(spa.end_time) = CURRENT_DATE
          </when>
          <when test="precondition != null and precondition == 2">
              AND DATE(spa.end_time) = DATE_SUB(CURRENT_DATE, INTERVAL 1 DAY)
          </when>
          <when test="precondition != null and precondition == 3">
              AND YEARWEEK(spa.end_time, 1) = YEARWEEK(NOW(), 1)
          </when>
          <when test="precondition != null and precondition == 4">
              AND DATE(spa.end_time) >= DATE_SUB(CURRENT_DATE, INTERVAL 14 DAY)
          </when>
      </choose>
      <if test="queryStartDate != null">
          AND DATE(spa.end_time) >= DATE(#{queryStartDate})
      </if>
      <if test="queryEndDate != null">
          AND DATE(spa.end_time) &lt;= DATE(#{queryEndDate})
      </if>
      GROUP BY
      spa.child_id
      ORDER BY passCount DESC
    </select>
    <select id="getPlanActivityListByPlanId" resultType="com.bmh.project.st.vo.StPlanProjectVo">
        SELECT DISTINCT
            spa.id ,
            spa.level_name AS levelName,
            spa.domain_id AS domainId,
            spa.domain_name AS domainName,
            spa.project_id AS projectId,
            spa.project_name AS projectName,
            spa.activity_id AS activityId,
            spa.activity_name AS activityName
        FROM
            st_plan_activity spa
                INNER JOIN st_plan_activity_target spat ON spa.id = spat.plan_activity_id
        WHERE
            spa.plan_id = #{planId}
          <if test="status!=null">
              AND spat.`status` = #{status}
          </if>
        ORDER BY spa.activity_id
    </select>
</mapper>
