<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.pdms.mapper.YcxPdmsDictcMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.pdms.model.YcxPdmsDictc">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="pdms_type" jdbcType="INTEGER" property="pdmsType" />
    <result column="min_score" jdbcType="INTEGER" property="minscore" />
    <result column="max_score" jdbcType="INTEGER" property="maxscore" />
    <result column="equal_age" jdbcType="INTEGER" property="equalAge" />
  </resultMap>
    <select id="getEqualAge" resultType="java.lang.Integer">
      select dict.equal_age
	  from ycx_pdms_dict_c
	  as dict
	  where dict.pdms_type = #{type}
	  and (dict.min_score &lt;= #{score} and dict.max_score &gt;= #{score})
    </select>
</mapper>