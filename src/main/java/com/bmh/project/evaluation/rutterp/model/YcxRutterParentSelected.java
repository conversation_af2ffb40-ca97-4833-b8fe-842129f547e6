package com.bmh.project.evaluation.rutterp.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_rutterp_selected")
public class YcxRutterParentSelected implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 评测记录id
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 问题id
     */
    @Column(name = "rutterp_id")
    private Integer rutterpId;

    /**
     * 选中项
     */
    @Column(name = "answer")
    private String answer;

    /**
     * 得分
     */
    @Column(name = "score")
    private Integer score;

    /**
     * 状态(0.无效 1.有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;

    /**
     * 修改时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    private static final long serialVersionUID = 1L;
}