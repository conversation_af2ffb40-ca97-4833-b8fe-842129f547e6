<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.cabs.mapper.YcxCabsMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.cabs.model.YcxCabs">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />


    <collection property="options" ofType="com.bmh.project.evaluation.cabs.model.YcxCabsOption" >
      <result column="cabs_id" jdbcType="INTEGER" property="cabsId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="value" jdbcType="VARCHAR" property="value" />
    </collection>

  </resultMap>


<!--  List<YcxCars> getList();-->
  <select id="getList" resultMap="BaseResultMap" >
    select cabs.*
    , op.cabs_id
    , op.name
    , op.value
    from ycx_cabs as cabs
    left join ycx_cabs_option as op
    on op.cabs_id = cabs.id
    order by cabs.order_num asc, op.id asc, op.value asc
  </select>

</mapper>