<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.chat.mapper.YcxChatMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.chat.model.YcxChat">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="is_core" jdbcType="INTEGER" property="isCore" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.chat.model.YcxChatOption">
      <result column="chat_id" jdbcType="INTEGER" property="chatId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="value" jdbcType="VARCHAR" property="value" />
    </collection>


  </resultMap>


<!--  List<YcxChat> getChatListByType(Integer type);-->
  <select id="getChatListByType" parameterType="Integer" resultMap="BaseResultMap" >
    select chat.*
    , op.chat_id
    , op.name
    , op.value
    from ycx_chat as chat
    inner join ycx_chat_option as op
    on op.chat_id = chat.id
    where chat.type = #{type}
    and chat.status = 1
    order by chat.order_num asc, op.id asc
  </select>



</mapper>