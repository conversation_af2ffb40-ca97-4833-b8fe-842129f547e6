<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.user.mapper.YcxChildrenTeacherMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.user.model.YcxChildrenTeacher">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="attend_type" jdbcType="INTEGER" property="attendType" />
    <result column="expire_date" jdbcType="TIMESTAMP" property="expireDate" />
    <result column="course_type" jdbcType="VARCHAR" property="courseType" />
  </resultMap>
    <update id="delAllRelationByChildId">
        update ycx_children_teacher set status = 0 where child_id = #{childId} and status = 1 and attend_type = 1
    </update>

    <select id="getChildsByUserId" resultMap="BaseResultMap">
        select t.child_id,t.attend_type
        from ycx_children_teacher t
        where t.status=1
        and t.user_id = #{userId}
        and date(t.expire_date)>=date(NOW())
        and  FIND_IN_SET(#{courseType},t.course_type)>0
        group by t.child_id

    </select>

</mapper>
