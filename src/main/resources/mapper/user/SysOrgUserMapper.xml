<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.user.mapper.SysOrgUserMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.user.model.SysOrgUser">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="erp_org_doctor_id" jdbcType="INTEGER" property="erpOrgDoctorId" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />

    <result column="org_name" jdbcType="VARCHAR" property="orgName" />
    <result column="org_short_name" jdbcType="VARCHAR" property="orgShortName" />
    <result column="org_logo" jdbcType="VARCHAR" property="orgLogo" />
  </resultMap>

  <select id="getOrgListByUserId" resultMap="BaseResultMap">
      select sou.*, so.name as org_name, so.short_name as org_short_name,so.logo as org_logo
      from sys_org_user sou
               left join sys_org so on sou.org_id = so.id and so.status = 1
      where sou.user_id = #{userId}
      and sou.apply_status = 1
  </select>
</mapper>