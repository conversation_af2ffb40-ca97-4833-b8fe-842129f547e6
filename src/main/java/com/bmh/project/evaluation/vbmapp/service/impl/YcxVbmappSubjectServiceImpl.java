package com.bmh.project.evaluation.vbmapp.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.vbmapp.model.YcxVbmappSubject;
import com.bmh.project.evaluation.vbmapp.mapper.YcxVbmappSubjectMapper;
import com.bmh.project.evaluation.vbmapp.model.YcxVbmappSubjectBook;
import com.bmh.project.evaluation.vbmapp.model.YcxVbmappSubjectOption;
import com.bmh.project.evaluation.vbmapp.service.YcxVbmappSubjectBookService;
import com.bmh.project.evaluation.vbmapp.service.YcxVbmappSubjectOptionService;
import com.bmh.project.evaluation.vbmapp.service.YcxVbmappSubjectService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * YcxVbmappSubjectService对应的实现类
 *
 * <AUTHOR>
 * @date 2022年11月21日 20:20:23
 */
@Service
public class YcxVbmappSubjectServiceImpl extends BaseServiceImpl<YcxVbmappSubject> implements YcxVbmappSubjectService {
    @Resource
    private YcxVbmappSubjectMapper ycxVbmappSubjectMapper;

    @Resource
    private YcxVbmappSubjectOptionService optionService;

    @Resource
    private YcxVbmappSubjectBookService bookService;

    @Override
    public List<YcxVbmappSubject> getSubjectByRealm(Integer realmId) {
        Example example=new Example(YcxVbmappSubject.class);
        example.createCriteria().andEqualTo("realmId",realmId);
        example.orderBy("orderNum").asc();
        List<YcxVbmappSubject> list = this.selectByExample(example);

        list.forEach(p->{
            //题目选项
            List<YcxVbmappSubjectOption> subjectOptions = optionService.getSubjectOption(p.getId());
            p.setSubjectOptions(subjectOptions);
            //题目指南
            List<YcxVbmappSubjectBook> subjectBooks= bookService.getSubjectBooks(p.getId());
            //第一条加上题目
            YcxVbmappSubjectBook subject = new YcxVbmappSubjectBook();
            subject.setLabel("题目");
            subject.setContent(p.getTitle()+"("+p.getLabel()+")");
            subject.setOrderNum(0);
            subject.setType(1);
            subjectBooks.add(0,subject);
            p.setSubjectBooks(subjectBooks);
        });
        return list;
    }
}