<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.supervisor.mapper.SysSupervisorScopeMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.supervisor.model.SysSupervisorScope">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="supervisor_id" jdbcType="INTEGER" property="supervisorId" />
    <id column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="orgName" property="orgName" jdbcType="VARCHAR" />
    <result column="orgShortName" property="orgShortName" jdbcType="VARCHAR" />
    <result column="erpOrgId" property="erpOrgId" jdbcType="INTEGER" />
    <result column="supervisorName" property="supervisorName" jdbcType="VARCHAR" />
  </resultMap>

  <select id="getListBySupervisorId" resultMap="BaseResultMap">
    select ss.supervisor_id,
    ss.org_id,
    so.name as orgName,
    so.short_name as orgShortName,
    so.erp_org_id as erpOrgId
    from sys_supervisor_scope ss
    left join sys_org so on ss.org_id = so.id
    where ss.supervisor_id = #{supervisorId}
    and so.status = 1
  </select>

  <select id="getListByOrgId" resultMap="BaseResultMap">
    select ss.supervisor_id,
           ss.org_id,
           so.name as supervisorName
    from sys_supervisor_scope ss
           inner join sys_supervisor so on ss.supervisor_id = so.id
    where so.status=1
          and ss.org_id = #{orgId}
  </select>

</mapper>