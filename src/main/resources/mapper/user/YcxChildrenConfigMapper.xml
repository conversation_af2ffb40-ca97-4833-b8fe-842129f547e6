<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.user.mapper.YcxChildrenConfigMapper">

    <resultMap type="com.bmh.project.user.model.YcxChildrenConfig" id="YcxChildrenConfigMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="childId" column="child_id" jdbcType="INTEGER"/>
        <result property="abaStatus" column="aba_status" jdbcType="INTEGER"/>
        <result property="stStatus" column="st_status" jdbcType="INTEGER"/>
        <result property="eaStatus" column="ea_status" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
    </resultMap>


</mapper>

