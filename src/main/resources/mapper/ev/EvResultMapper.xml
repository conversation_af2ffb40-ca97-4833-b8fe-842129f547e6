<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ev.mapper.EvResultMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ev.model.EvResult">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="result_no" jdbcType="VARCHAR" property="resultNo" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="teacher_id" jdbcType="INTEGER" property="teacherId" />
    <result column="total_score" jdbcType="DECIMAL" property="totalScore" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="report_url" jdbcType="VARCHAR" property="reportUrl" />
    <result column="image_url" jdbcType="VARCHAR" property="imageUrl" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
    <select id="selectPepHistoryList" resultType="com.bmh.project.report.vo.ReportLMTTitleVo">
        select
            id as recordId, "PEP" as shortTitle, "PEP评估" as title, create_time as createdTime
        from ev_result
        where children_id = #{id}
          and status = 1
          and is_delete = 0
          and type = 1
        order by create_time
    </select>
</mapper>

