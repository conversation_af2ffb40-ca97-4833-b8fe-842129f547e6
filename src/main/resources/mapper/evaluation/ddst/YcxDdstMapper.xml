<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.ddst.mapper.YcxDdstMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.ddst.model.YcxDdst">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="tips" jdbcType="VARCHAR" property="tips" />
    <result column="min_month" jdbcType="VARCHAR" property="minMonth" />
    <result column="max_month" jdbcType="VARCHAR" property="maxMonth" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>