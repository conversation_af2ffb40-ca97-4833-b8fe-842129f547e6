<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.epqc.mapper.YcxEpqcDictMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.epqc.model.YcxEpqcDict">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="epqc_type" jdbcType="INTEGER" property="epqcType" />
    <result column="gender" jdbcType="INTEGER" property="gender" />
    <result column="age" jdbcType="INTEGER" property="age" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="standardscor" jdbcType="INTEGER" property="standardscor" />
  </resultMap>
  <select id="getEpqcDict" resultType="java.lang.Integer">
    select e.standardscor
      from ycx_epqc_dict as e
      where e.epqc_type = #{epqcType}
      and e.gender = #{gender}
      and e.age = #{age}
      and e.score = #{score}
  </select>


</mapper>