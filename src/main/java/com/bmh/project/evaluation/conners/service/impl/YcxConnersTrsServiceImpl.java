package com.bmh.project.evaluation.conners.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.conners.mapper.YcxConnersTrsMapper;
import com.bmh.project.evaluation.conners.model.YcxConnersTrs;
import com.bmh.project.evaluation.conners.service.YcxConnersTrsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * YcxConnersTrsService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月10日 14:43:46
 */
@Service
public class YcxConnersTrsServiceImpl extends BaseServiceImpl<YcxConnersTrs> implements YcxConnersTrsService {
    @Resource
    private YcxConnersTrsMapper ycxConnersTrsMapper;

    /**
     * 根据类型获取问题
     *
     * @param type 类型(1品行问题，2多动，3注意力不集中-被动， 4其他)
     * @return
     */
    @Override
    public List<YcxConnersTrs> getListByType (Integer type) {
        return ycxConnersTrsMapper.getListByType (type);
    }
}