<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaDailyGoalDataMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaDailyGoalData">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="plan_project_id" jdbcType="INTEGER" property="planProjectId" />
    <result column="plan_project_goal_id" jdbcType="INTEGER" property="planProjectGoalId" />
    <result column="short_goal_id" jdbcType="INTEGER" property="shortGoalId" />
    <result column="short_goal_name" jdbcType="VARCHAR" property="shortGoalName" />
    <result column="assist_method" jdbcType="VARCHAR" property="assistMethod" />
    <result column="target_result" jdbcType="LONGVARCHAR" property="targetResult" />
    <result column="record_type" jdbcType="INTEGER" property="recordType" />
    <result column="course_ids" jdbcType="VARCHAR" property="courseIds" />
    <result column="course_project_ids" jdbcType="VARCHAR" property="courseProjectIds" />
    <result column="course_project_goal_ids" jdbcType="VARCHAR" property="courseProjectGoalIds" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_level" jdbcType="INTEGER" property="projectLevel" />
    <result column="pass_rate" jdbcType="DOUBLE" property="passRate" />
    <result column="is_continuous_pass" jdbcType="INTEGER" property="isContinuousPass" />
    <result column="is_fail" jdbcType="INTEGER" property="isFail" />
    <result column="is_slow" jdbcType="INTEGER" property="isSlow" />
    <result column="is_step_pass" jdbcType="INTEGER" property="isStepPass" />
    <result column="try_item_total" jdbcType="INTEGER" property="tryItemTotal" />
    <result column="try_pass_count" jdbcType="INTEGER" property="tryPassCount" />

  </resultMap>


  <select id="getRecently14Date" resultType="java.util.Date">
    select date
    from aba_daily_goal_data
    where child_id = #{childId}
    and plan_project_id = #{planProjectId}
    group by date
    order by date desc
      limit 14
  </select>

  <select id="getMonthDate" resultType="java.lang.String">
    select DATE_FORMAT(date,'%Y-%m-%d')
    from aba_daily_goal_data
    where child_id = #{childId}
      and DATE_FORMAT(date, '%Y-%m') = #{month}
    group by date
    order by date desc
  </select>

  <select id="getSuperviseDailyDataVo" resultType="com.bmh.project.aba.vo.AbaSuperviseDailyDataVo">
    SELECT dd.plan_project_id        AS planProjectId,
           dd.project_id             AS projectId,
           dd.plan_project_goal_id   AS planProjectGoalId,
           dd.record_type   AS recordType,
           dd.date                   AS date,
           dd.child_id               AS childId,
           IF(apph.id is null, 0, 1) AS isAdjust,
           dd.short_goal_id         AS shortGoalId,
           dd.short_goal_name       AS shortGoalName,
           dd.assist_method       AS assistMethod,
           dd.pass_rate as passRate,
           dd.try_pass_count as tryPassCount,
           dd.try_item_total as tryItemTotal
    FROM aba_daily_goal_data dd
      LEFT JOIN aba_plan_project_goal_history apph ON dd.plan_id = apph.plan_id
      AND dd.plan_project_id = apph.plan_project_id
      AND dd.plan_project_goal_id = apph.plan_project_goal_id
      AND dd.record_type = apph.record_type
      AND dd.date = DATE(apph.create_time)
    WHERE dd.plan_project_id = #{planProjectId}
      AND dd.date >= DATE(#{recently14Date})
    ORDER BY dd.date ASC;
  </select>

  <select id="getSmallGoalTotalList" resultType="com.bmh.project.aba.vo.AbaSmallGoalVo">
      SELECT pp.domain_id as  domainId,
                      pp.domain_name as  domainName,
                      pp.project_id as  projectId,
                      pp.project_name as  projectName,
                      cp.short_goal_id as  shortGoalId,
                      cp.short_goal_name as  shortGoalName,
                      rd.target_name as targetName,
                      min(pp.start_time) as startTime,
                      max(pp.end_time) as endTime,
                      1 as smallGoalNum
      from  aba_course_record  rd
                INNER JOIN aba_course_project_goal cp on  rd.course_project_goal_id = cp.id
                INNER JOIN aba_plan_project pp on cp.plan_project_id=pp.id
      where cp.status>0
        and cp.child_id = #{childId}
        and cp.record_type = #{recordType}
      group by pp.domain_id ,
              pp.domain_name,
              pp.project_id,
              pp.project_name ,
              cp.short_goal_id ,
              cp.short_goal_name ,
              rd.target_name
      ORDER BY pp.domain_id,pp.project_id,cp.short_goal_id
  </select>
    <select id="getPlanProjectId" resultType="java.lang.Integer">
        SELECT plan_project_id
        FROM aba_daily_goal_data
        WHERE plan_id = #{planId}
          AND child_id = #{childrenId}
          AND record_type = #{recordType}
        GROUP BY plan_project_id
    </select>
    <select id="getDataByPlanProjectIdAndDate" resultType="com.bmh.project.aba.model.AbaDailyGoalData">
        SELECT
        plan_project_id,
        plan_project_goal_id,
        project_id,
        domain_name,
        `date`,
        project_name,
        project_level,
        short_goal_name,
        pass_rate,
        target_result,
        try_pass_count,
        assist_method
        FROM aba_daily_goal_data
        WHERE plan_project_id = #{planProjectId}
        <if test="recently14Date != null and recently14Date.size() > 0">
            AND `date` IN
            <foreach item="date" collection="recently14Date" open="(" separator="," close=")">
                #{date}
            </foreach>
        </if>
        ORDER BY `date` DESC
    </select>
    <select id="get14Date" resultType="com.bmh.project.aba.model.AbaDailyGoalData">
        SELECT
            t.plan_project_id AS PlanProjectId,
            t.plan_project_goal_id AS PlanProjectGoalId,
            t.domain_name AS DomainName,
            t.`date` AS Date,
            t.project_name AS ProjectName,
            t.project_level AS ProjectLevel,
            t.short_goal_name AS ShortGoalName,
            t.pass_rate AS PassRate,
            t.target_result AS TargetResult,
            t.try_pass_count AS TryPassCount,
            t.assist_method AS AssistMethod
        FROM (
            SELECT
            *,
            @rank := IF(@prev_project = plan_project_id, @rank + 1, 1) AS RankDesc,
            @prev_project := plan_project_id AS DummyPrev
            FROM (
            SELECT
            @rank := 0,
            @prev_project := NULL,
            aba.*
            FROM aba_daily_goal_data aba
            WHERE child_id = #{childrenId}
            AND plan_id = #{planId}
            AND record_type = #{recordType}
            ORDER BY plan_project_id, `date` DESC
            ) AS sorted
            ) AS t
        WHERE RankDesc &lt;= 14
        ORDER BY t.plan_project_id, t.`date`

    </select>

    <select id="getLastChildData" resultMap="BaseResultMap">
        select id,course_project_goal_ids,pass_rate,target_result
        from aba_daily_goal_data
        where plan_project_id = #{planProjectId}
          and plan_project_goal_id = #{planProjectGoalId}
          and record_type = #{recordType}
        order by date desc
        limit 1
    </select>

</mapper>
