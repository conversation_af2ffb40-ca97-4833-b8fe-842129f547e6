package com.bmh.project.evaluation.gesell.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.gesell.model.YcxGesellSelected;

import java.util.List;

/**
 * ycx_gesell_selecteds表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年06月11日 11:38:14
 */
public interface YcxGesellSelectedService extends BaseService<YcxGesellSelected> {

    void saveSelecteds(List<YcxGesellSelected> gesellSelecteds, Integer recordId);

}