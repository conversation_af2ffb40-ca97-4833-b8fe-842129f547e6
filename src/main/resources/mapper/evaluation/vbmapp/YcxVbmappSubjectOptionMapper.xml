<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.vbmapp.mapper.YcxVbmappSubjectOptionMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.vbmapp.model.YcxVbmappSubjectOption">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="subject_id" jdbcType="INTEGER" property="subjectId" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="keyword" jdbcType="VARCHAR" property="keyword" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="is_default" jdbcType="INTEGER" property="isDefault" />
    <result column="is_quick" jdbcType="INTEGER" property="isQuick" />
  </resultMap>
</mapper>