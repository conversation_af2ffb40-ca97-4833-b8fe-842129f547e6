package com.bmh.project.evaluation.behavior.model;

import com.bmh.project.common.model.YcxResult;
import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@Table(name = "ycx_behavior_result")
public class YcxBehaviorResult extends YcxResult implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 记录ID
     */
    @Column(name = "record_id")
    private Integer recordId;

    /**
     * 孩子年龄(周)
     */
    @Column(name = "children_age")
    private BigDecimal childrenAge;

    /**
     * 孩子ID
     */
    @Column(name = "children_id")
    private Integer childrenId;

    /**
     * 单位ID
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 大运动
     */
    @Column(name = "da_type1")
    private String daType1;

    /**
     * 精细动作
     */
    @Column(name = "da_type2")
    private String daType2;

    /**
     * 适应能力
     */
    @Column(name = "da_type3")
    private String daType3;

    /**
     * 语言
     */
    @Column(name = "da_type4")
    private String daType4;

    /**
     * 社会行为
     */
    @Column(name = "da_type5")
    private String daType5;

    /**
     * DA平均分
     */
    @Column(name = "da_avg")
    private String daAvg;

    /**
     * 大运动
     */
    @Column(name = "dq_type1")
    private String dqType1;

    /**
     * 精细动作
     */
    @Column(name = "dq_type2")
    private String dqType2;

    /**
     * 适应能力
     */
    @Column(name = "dq_type3")
    private String dqType3;

    /**
     * 语言
     */
    @Column(name = "dq_type4")
    private String dqType4;

    /**
     * 社会行为
     */
    @Column(name = "dq_type5")
    private String dqType5;

    /**
     * DQ平均分
     */
    @Column(name = "dq_avg")
    private String dqAvg;

    /**
     * 建议
     */
    @Column(name = "recom")
    private String recom;

    /**
     * 用于展示历史档案的字符串
     */
    @Column(name = "result_his")
    private String resultHis;

    /**
     * 操作人ID
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;



    /**
     * behavior 选中项
     */
    @Transient
    private List<YcxBehaviorSelected> behaviorSelecteds;

}