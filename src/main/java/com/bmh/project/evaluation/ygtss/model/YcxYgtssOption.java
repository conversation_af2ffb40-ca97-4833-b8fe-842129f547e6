package com.bmh.project.evaluation.ygtss.model;

import java.io.Serializable;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_ygtss_option")
public class YcxYgtssOption implements Serializable {
    /**
     * 主键Id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    @Column(name = "ygtss_id")
    private Integer ygtssId;

    /**
     * 名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 是否是单选框(1,0)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 值
     */
    @Column(name = "value")
    private String value;

    /**
     * 分数
     */
    @Column(name = "score")
    private Integer score;

    private static final long serialVersionUID = 1L;
}