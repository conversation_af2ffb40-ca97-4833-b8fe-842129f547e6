package com.bmh.project.supervisor.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.supervisor.mapper.SysSupervisorScopeMapper;
import com.bmh.project.supervisor.model.SysSupervisorScope;
import com.bmh.project.supervisor.service.SysSupervisorScopeService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * SysSupervisorScopeService对应的实现类
 *
 * <AUTHOR>
 * @date 2023年07月24日 11:21:32
 */
@Service
public class SysSupervisorScopeServiceImpl extends BaseServiceImpl<SysSupervisorScope> implements SysSupervisorScopeService {
    @Resource
    private SysSupervisorScopeMapper sysSupervisorScopeMapper;

    /**
     * 根据监管老师ID获取监管机构列表
     *
     * @param supervisorId 监管老师ID
     * @return
     */
    @Override
    public List<SysSupervisorScope> getListBySupervisorId (Integer supervisorId) {
        return sysSupervisorScopeMapper.getListBySupervisorId (supervisorId);
    }

    /**
     * 根据监管机构id获得监管老师列表
     * @param orgId 机构ID
     * @return
     */
    @Override
    public List<SysSupervisorScope> getListByOrgId(Integer orgId) {
        return sysSupervisorScopeMapper.getListByOrgId (orgId);
    }
}