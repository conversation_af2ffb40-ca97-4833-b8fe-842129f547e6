<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.cbcl.mapper.YcxCbclMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.cbcl.model.YcxCbcl">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="order_num" jdbcType="INTEGER" property="orderNum"/>
        <result column="level" jdbcType="INTEGER" property="level"/>
        <result column="parent_id" jdbcType="INTEGER" property="parentId"/>
        <result column="has_child" jdbcType="INTEGER" property="hasChild"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>

        <collection property="options" ofType="com.bmh.project.evaluation.cbcl.model.YcxCbclOption">
            <result column="cbcl_id" property="cbclId"/>
            <result column="name" property="name"/>
            <result column="value" property="value"/>
            <result column="score" property="score"/>
        </collection>
    </resultMap>

    <select id="getCbclListByType" resultMap="BaseResultMap">
        select cbcl.*
             , op.cbcl_id
             , op.name
             , op.value
             , op.score
        from ycx_cbcl as cbcl
            left join ycx_cbcl_option as op on op.cbcl_id = cbcl.id
        where cbcl.type = #{type} and cbcl.status = 1
        and cbcl.level = #{level}
        <if test="parentId != null">
            and cbcl.parent_id = #{parentId}
        </if>
        order by cbcl.order_num asc, op.value asc, op.id asc
    </select>
</mapper>