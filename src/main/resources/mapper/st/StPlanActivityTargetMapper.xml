<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.st.mapper.StPlanActivityTargetMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.st.model.StPlanActivityTarget">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="plan_activity_id" jdbcType="INTEGER" property="planActivityId" />
    <result column="target_id" jdbcType="INTEGER" property="targetId" />
    <result column="target_name" jdbcType="VARCHAR" property="targetName" />
      <result column="suggest_duration" jdbcType="VARCHAR" property="suggestDuration" />
      <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <insert id="insertBatchByPlanId">
      insert into st_plan_activity_target (plan_id, plan_activity_id, target_id, target_name, suggest_duration, status,
                                           create_user, create_time,
                                           update_time, update_user)
      select spa.plan_id,
             spa.id   as plan_activity_id,
             sat.id   as target_id,
             sat.name as target_name,
             sat.suggest_duration,
             1        as status,
             spa.create_user,
             spa.create_time,
             spa.update_time,
             spa.update_user
      from st_plan_activity spa
               left join st_activity_target sat on spa.activity_id = sat.activity_id and sat.is_delete = 0
      where spa.plan_id = #{planId}
  </insert>

  <insert id="insertBatchByActivityIds">
      insert into st_plan_activity_target (plan_id, plan_activity_id, target_id, target_name, suggest_duration, status,
                                           create_user, create_time,
                                           update_time, update_user)
      select spa.plan_id,
             spa.id   as plan_activity_id,
             sat.id   as target_id,
             sat.name as target_name,
             sat.suggest_duration,
             1        as status,
             spa.create_user,
             spa.create_time,
             spa.update_time,
             spa.update_user
      from st_plan_activity spa
               left join st_activity_target sat on spa.activity_id = sat.activity_id and sat.is_delete = 0
      where spa.id in (
      <foreach collection="planActivityIds" item="planActivityId" separator=",">
          #{planActivityId}
      </foreach>
      )
  </insert>

  <select id="getNamesByIds" resultType="java.lang.String">
      select target_name
      from st_plan_activity_target
      where id in (
      <foreach collection="ids" item="id" separator=",">
          #{id}
      </foreach>
      )
  </select>

    <select id="getTargetListByActivityId" resultMap="BaseResultMap">
        SELECT
            plan_activity_id,
            target_id,
            target_name,
            status
        FROM st_plan_activity_target
        WHERE plan_activity_id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by target_id
    </select>
</mapper>
