<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.book.mapper.YcxChildrenEvaluatingMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.book.model.YcxChildrenEvaluating">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_number" jdbcType="VARCHAR" property="recordNumber" />
    <result column="book_id" jdbcType="INTEGER" property="bookId" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="doctor_id" jdbcType="INTEGER" property="doctorId" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_id" jdbcType="INTEGER" property="createId" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="submit_time" jdbcType="TIMESTAMP" property="submitTime" />
  </resultMap>
    <select id="getProjectList" resultType="com.bmh.project.book.vo.EvaluatingProjectVo">
      SELECT p.id,p.classify,p.tag,p.short_title AS shortTitle,p.title,
      p.min_month AS minMonth,p.max_month AS maxMonth,p.order_num AS orderNum,
      IF(ep.project_id IS NULL,0,1) AS isSelected,b.month_age,
      IF(((b.month_age>=p.min_month OR p.min_month IS NULL) AND (b.month_age &lt;= p.max_month OR p.max_month IS NULL)),0,1) AS isDisabled
      FROM ycx_project p
      LEFT JOIN ycx_children_book b ON b.id= #{bookId}
      LEFT JOIN ycx_children_evaluating e ON e.book_id = b.id
      LEFT JOIN ycx_children_evaluating_project ep ON ep.project_id=p.id AND ep.evaluating_id=e.id and ep.is_delete = 0
      WHERE p.`status`=1
      ORDER BY classify ASC,p.order_num ASC
    </select>
    <select id="getEvaluatingProjectCount" resultType="com.bmh.project.book.vo.DoctorEvaluatingCountVo">
        SELECT e.id                           AS evaluatingId,
               e.book_id                      AS bookId,
               COUNT(ep.`status` = 0 OR NULL) AS evaluating0,
               COUNT(ep.`status` = 1 OR NULL) AS evaluating1,
               COUNT(ep.`status` = 2 OR NULL) AS evaluating2
        FROM ycx_children_evaluating_project ep
                 INNER JOIN ycx_children_evaluating e ON e.id = ep.evaluating_id
        where ep.is_delete = 0
            <choose>
                <when test="paramType == 1">
                    AND e.id = #{param}
                </when>
                <otherwise>
                    AND e.book_id = #{param}
                </otherwise>
            </choose>
    </select>
</mapper>