<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaPlanProjectGoalHistoryMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaPlanProjectGoalHistory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="plan_project_id" jdbcType="INTEGER" property="planProjectId" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="plan_project_goal_id" jdbcType="INTEGER" property="planProjectGoalId" />
    <result column="short_goal_id" jdbcType="INTEGER" property="shortGoalId" />
    <result column="short_goal_name" jdbcType="VARCHAR" property="shortGoalName" />
    <result column="assist_method" jdbcType="VARCHAR" property="assistMethod" />
    <result column="record_type" jdbcType="INTEGER" property="recordType" />
    <result column="opt_type" jdbcType="INTEGER" property="optType" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="remarks" jdbcType="VARCHAR" property="remarks" />
    <result column="target" jdbcType="LONGVARCHAR" property="target" />
    <result column="batch_no" jdbcType="INTEGER" property="batchNo" />
    <result column="user_type" jdbcType="INTEGER" property="userType" />
    <result column="is_confirm" jdbcType="INTEGER" property="isConfirm" />
    <result column="confirm_user" jdbcType="VARCHAR" property="confirmUser" />
    <result column="confirm_time" jdbcType="TIMESTAMP" property="confirmTime" />
  </resultMap>


  <select id="getProjectGoalLastHistory" resultMap="BaseResultMap">
    select *
    from aba_plan_project_goal_history
    where user_type=2
      and plan_project_goal_id = #{planProjectGoalId}
      and record_type = #{recordType}
      and date(create_time) &lt;= date(#{deadline})
      order by create_time desc
      limit 1
  </select>

  <select id="getProjectLastModifyDate" resultType="java.util.Date">
    select max(create_time)
    from aba_plan_project_goal_history
    where plan_project_goal_id = #{planProjectGoalId}
      and opt_type = 1
      and user_type = 2
      and date(create_time) &lt;= date(#{deadline})
  </select>

  <select id="getTrialProjectList" resultType="java.lang.Integer">
    select distinct project_id
    from aba_plan_project_goal_history his
    where his.child_id = #{childId}
      and his.record_type = 2
      and is_confirm = 0
  </select>

</mapper>
