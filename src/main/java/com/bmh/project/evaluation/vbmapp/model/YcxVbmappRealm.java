package com.bmh.project.evaluation.vbmapp.model;

import java.io.Serializable;
import java.util.List;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_vbmapp_realm")
public class YcxVbmappRealm implements Serializable {
    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 父级Id
     */
    @Column(name = "parent_id")
    private Integer parentId;

    /**
     * 名称
     */
    @Column(name = "name")
    private String name;

    /**
     * 排序号
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 分组
     */
    @Column(name = "group_no")
    private String groupNo;

    /**
     * 里程碑数量
     */
    @Column(name = "children_count")
    private Integer childrenCount;

    /**
     *
     */
    @Column(name = "is_quick")
    private Integer isQuick;
    /**
     * 里程碑列表
     */
    @Transient
    private List<YcxVbmappRealm> children;

    private static final long serialVersionUID = 1L;
}