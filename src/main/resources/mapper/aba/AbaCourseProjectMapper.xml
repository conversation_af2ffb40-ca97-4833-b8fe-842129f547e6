<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaCourseProjectMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaCourseProject">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="teacher_id" jdbcType="INTEGER" property="teacherId" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="plan_project_id" jdbcType="INTEGER" property="planProjectId" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_level" jdbcType="INTEGER" property="projectLevel" />
    <result column="long_goal" jdbcType="VARCHAR" property="longGoal" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="getListByCourseIds" resultMap="BaseResultMap">
      select acp.*
      from aba_course_project acp
<!--               left join aba_plan_project app on acp.plan_project_id = app.id-->
      where acp.course_id in
      <foreach collection="courseIds" item="courseId" open="(" separator="," close=")">
          #{courseId}
      </foreach>
<!--      and app.status = 1-->
  </select>
    <select id="getProjectCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT acp.plan_project_id)
        FROM aba_course_project acp
        LEFT JOIN  aba_course ac ON acp.course_id = ac.id
        WHERE ac.`status` = 2
          AND acp.create_time &gt; #{startTime}
          AND acp.create_time &lt; #{endTime}
          <if test="orgId != 0">
            AND acp.org_id = #{orgId}
          </if>
        <if test="0==orgId">
            AND acp.org_id NOT IN (1,19,43,65)
        </if>
    </select>
    <select id="getShortGoalCount" resultType="java.lang.Integer">
        SELECT COUNT(DISTINCT acpg.plan_project_goal_id)
        FROM aba_course_project_goal acpg
        LEFT JOIN aba_course ac ON acpg.course_id = ac.id
        WHERE ac.`status` = 2
          AND acpg.create_time &gt; #{startTime}
          AND acpg.create_time &lt; #{endTime}
          <if test="orgId!=0">
            AND acpg.org_id = #{orgId}
          </if>
        <if test="0==orgId">
            AND acpg.org_id NOT IN (1,19,43,65)
        </if>
    </select>
</mapper>
