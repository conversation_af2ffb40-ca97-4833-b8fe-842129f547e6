<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ev.mapper.EvPepPowerDomainResultMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ev.model.EvPepPowerDomainResult">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="result_id" jdbcType="INTEGER" property="resultId" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="raw_score" jdbcType="INTEGER" property="rawScore" />
    <result column="dev_age" jdbcType="INTEGER" property="devAge" />
    <result column="pass_score" jdbcType="INTEGER" property="passScore" />
    <result column="half_pass_score" jdbcType="INTEGER" property="halfPassScore" />
    <result column="dev_age_content" jdbcType="VARCHAR" property="devAgeContent" />
    <result column="percentage_series" jdbcType="VARCHAR" property="percentageSeries" />
    <result column="adapt_level" jdbcType="VARCHAR" property="adaptLevel" />
    <result column="standard_score" jdbcType="INTEGER" property="standardScore" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

    <select id="getSynthesisScore" resultType="com.bmh.project.ev.vo.PepSynthesisScoreVo">
        select d.synthesis_domain as synthesisDomain,
               sum(r.standard_score) as standardScoreCount,
               round(sum(r.dev_age)/count(d.synthesis_domain),1) as devAge
        from ev_pep_power_domain_result r
                 inner join  ev_pep_domain d on r.domain_id = d.id and d.is_delete=0
        where r.is_delete = 0
          and r.result_id = #{resultId}
        group by d.synthesis_domain
        having d.synthesis_domain is not null

    </select>

    <select id="getPowerDomainResultByResultId" resultType="com.bmh.project.ev.vo.PepPowerDomainResultVo">
        select r.result_id as resultId,
               d.id as domainId,
               d.quiz_domain as quizDomain,
               d.synthesis_domain as synthesisDomain,
               d.short_power_domain as shortPowerDomain,
               d.power_domain as powerDomain,
               d.is_cal_dev_age as isCalDevAge,
               r.raw_score as rawScore,
               r.dev_age as devAge,
               r.pass_score as passScore,
               r.half_pass_score as halfPassScore,
               r.dev_age_content as devAgeContent,
               r.percentage_series as percentageSeries,
               r.adapt_level as adaptLevel,
               r.standard_score as standardScore
        from ev_pep_power_domain_result r
                 inner join  ev_pep_domain d on r.domain_id = d.id and d.is_delete=0
        where r.is_delete = 0
          and r.result_id = #{resultId}

    </select>

</mapper>
