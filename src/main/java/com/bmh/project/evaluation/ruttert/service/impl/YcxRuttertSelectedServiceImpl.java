package com.bmh.project.evaluation.ruttert.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.ruttert.mapper.YcxRuttertSelectedMapper;
import com.bmh.project.evaluation.ruttert.model.YcxRuttertSelected;
import com.bmh.project.evaluation.ruttert.service.YcxRuttertSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxRuttertSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月17日 09:39:00
 */
@Service
public class YcxRuttertSelectedServiceImpl extends BaseServiceImpl<YcxRuttertSelected> implements YcxRuttertSelectedService {
    @Resource
    private YcxRuttertSelectedMapper ycxRuttertSelectedMapper;

    @Override
    public void saveSelected(List<YcxRuttertSelected> selecteds, Integer recordId) {
        selecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreatedTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        insertList(selecteds);
    }

    @Override
    public List<YcxRuttertSelected> getSelecteds(Integer recordId) {
        return ycxRuttertSelectedMapper.getSelecteds(recordId);
    }
}