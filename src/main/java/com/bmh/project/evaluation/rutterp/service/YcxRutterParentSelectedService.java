package com.bmh.project.evaluation.rutterp.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.rutterp.model.YcxRutterParentSelected;

import java.util.List;

/**
 * ycx_rutter-parent_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月16日 18:13:51
 */
public interface YcxRutterParentSelectedService extends BaseService<YcxRutterParentSelected> {

    void saveSelected(List<YcxRutterParentSelected> selecteds, Integer recordId);

    List<YcxRutterParentSelected> getSelecteds(Integer recordId);
}