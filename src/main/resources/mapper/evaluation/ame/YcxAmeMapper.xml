<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.ame.mapper.YcxAmeMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.ame.model.YcxAme">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.ame.model.YcxAmeOption" >
      <result column="ame_id" jdbcType="INTEGER" property="ameId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="value" jdbcType="VARCHAR" property="value" />
      <result column="score" jdbcType="VARCHAR" property="score" />
    </collection>
  </resultMap>

  <!--获取问题列表-->
    <select id="getList" resultMap="BaseResultMap">
      select
        ame.*,
        op.ame_id,
        op.name,
        op.value,
        op.score
      from
        ycx_ame as ame
          left join
        ycx_ame_option as op
        on
          op.ame_id = ame.id
      order by
        ame.order_num asc,op.score desc,op.id asc
    </select>
</mapper>