package com.bmh.project.evaluation.ybocs.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.record.model.YcxChildrenRecord;
import com.bmh.project.evaluation.ybocs.mapper.YcxYbocsResultMapper;
import com.bmh.project.evaluation.ybocs.model.YcxYbocsResult;
import com.bmh.project.evaluation.ybocs.service.YcxYbocsResultService;
import javax.annotation.Resource;

import com.bmh.project.evaluation.ybocs.service.YcxYbocsSelectedService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * YcxYbocsResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月17日 11:17:05
 */
@Service("40")
public class YcxYbocsResultServiceImpl extends BaseServiceImpl<YcxYbocsResult> implements YcxYbocsResultService, YcxResultService<YcxYbocsResult> {
    @Resource
    private YcxYbocsResultMapper ycxYbocsResultMapper;
    @Resource
    private YcxYbocsSelectedService selectedService;

    /**
     * 保存评测结果
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxYbocsResult result = JSONUtil.toBean(childrenRecord.getResultStr(), YcxYbocsResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge());
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setOperatrId(SecurityUtil.getUserId());
        result.setCreareTime(new Date());
        ycxYbocsResultMapper.insert(result);

        selectedService.saveSelected(result.getSelecteds(),result.getRecordId());
    }

    /**
     * 获取评测结果
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxYbocsResult getResult(Integer recordId) {
        Example example = new Example(YcxYbocsResult.class);
        example.createCriteria().andEqualTo("recordId",recordId);
        List<YcxYbocsResult> list = this.selectByExample(example);
        if (list != null && list.size() > 0) {
            YcxYbocsResult result = list.get(0);
            result.setSelecteds(selectedService.getSelecteds(recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     * @param recordId 记录ID
     * @param recom 建议
     */
    @Override
    public void updateRecome(Integer recordId, String recom) {
        YcxYbocsResult result = new YcxYbocsResult();
        result.setRecom(recom);
        Example example = new Example(YcxYbocsResult.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("recordId",recordId);
        ycxYbocsResultMapper.updateByExampleSelective(result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}