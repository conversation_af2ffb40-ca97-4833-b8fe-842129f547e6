<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.homework.mapper.AbaHomeworkMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.homework.model.AbaHomework">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="getHomeWorkListByIds" resultType="com.bmh.project.homework.vo.AbaHwDailyVo">
    select h.id as hwId,
    h.create_user     as teacherName,
    h.create_time    as createTime,
    h.status
    from aba_homework h
    where  h.id in
    <foreach collection="hwIds" item="hwId" open="(" separator="," close=")">
      #{hwId}
    </foreach>
    order by h.create_time desc
  </select>

</mapper>
