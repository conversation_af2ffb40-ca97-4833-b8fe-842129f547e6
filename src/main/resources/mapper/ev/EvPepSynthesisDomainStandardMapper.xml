<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ev.mapper.EvPepSynthesisDomainStandardMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ev.model.EvPepSynthesisDomainStandard">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="synthesis_domain" jdbcType="VARCHAR" property="synthesisDomain" />
    <result column="standard_score_count" jdbcType="INTEGER" property="standardScoreCount" />
    <result column="percentage_series" jdbcType="VARCHAR" property="percentageSeries" />
    <result column="adapt_level" jdbcType="VARCHAR" property="adaptLevel" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>
</mapper>
