<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.adir.mapper.YcxAdirMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.adir.model.YcxAdir">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />


    <collection property="options" ofType="com.bmh.project.evaluation.adir.model.YcxAdirOption">
      <result column="adir_id" jdbcType="INTEGER" property="adirId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="value" jdbcType="VARCHAR" property="value" />
    </collection>
    
  </resultMap>


<!--  List<YcxAdir> getAdirListByType(Integer type);-->
  <select id="getAdirListByType" parameterType="Integer" resultMap="BaseResultMap" >
    select adir.*
    , op.adir_id
    , op.name
    , op.value
    from ycx_adir as adir
    inner join ycx_adir_option as op
    on op.adir_id = adir.id
    where adir.type = #{type}
    and adir.status = 1
    order by adir.order_num asc,op.value asc, op.id asc
  </select>

</mapper>