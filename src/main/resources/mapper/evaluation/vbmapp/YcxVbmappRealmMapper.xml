<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.vbmapp.mapper.YcxVbmappRealmMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.vbmapp.model.YcxVbmappRealm">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="parent_id" jdbcType="INTEGER" property="parentId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="group_no" jdbcType="VARCHAR" property="groupNo" />
    <result column="children_count" jdbcType="INTEGER" property="childrenCount" />
    <result column="is_quick" jdbcType="INTEGER" property="isQuick" />
  </resultMap>
<!--设置字节点数量 没有代码调用（手动执行）-->
  <update id="updateChildrenCount">
    UPDATE ycx_vbmapp_realm a ,(
      SELECT parent_id,COUNT(1) AS children_count FROM ycx_vbmapp_realm
      WHERE parent_id>0
      GROUP BY parent_id
      ) b
    SET a.children_count=b.children_count
    WHERE a.id=b.parent_id
  </update>
</mapper>