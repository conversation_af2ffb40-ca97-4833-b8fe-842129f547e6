<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.rutterp.mapper.YcxRutterParentSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.rutterp.model.YcxRutterParentSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="rutterp_id" jdbcType="INTEGER" property="rutterpId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
    <select id="getSelecteds" resultMap="BaseResultMap">
      select sele.*,
      rutterp.content,
      rutterp.order_num,
      op.name as optionName
      from
        ycx_rutterp_selected as sele
          inner join
        ycx_rutterp as rutterp
        on
          rutterp.id = sele.rutterp_id
          left join
        ycx_rutterp_option as op
        on
            op.rutterp_id = rutterp.id
            and
            op.value = sele.answer
      where
        sele.record_id = #{recordId}
      order by rutterp.order_num asc
    </select>
</mapper>