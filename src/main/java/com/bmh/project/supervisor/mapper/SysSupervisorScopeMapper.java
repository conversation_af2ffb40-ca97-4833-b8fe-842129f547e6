package com.bmh.project.supervisor.mapper;

import com.bmh.common.base.BaseMapper;
import com.bmh.project.supervisor.model.SysSupervisorScope;

import java.util.List;

public interface SysSupervisorScopeMapper extends BaseMapper<SysSupervisorScope> {

    /**
     * 根据监管老师ID获取监管机构列表
     * @param supervisorId 监管老师ID
     * @return
     */
    List<SysSupervisorScope> getListBySupervisorId(Integer supervisorId);


    /**
     * 根据监管机构id获得监管老师列表
     * @param orgId
     * @return
     */
    List<SysSupervisorScope> getListByOrgId(Integer orgId);
}