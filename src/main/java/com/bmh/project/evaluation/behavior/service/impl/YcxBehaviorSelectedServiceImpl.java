package com.bmh.project.evaluation.behavior.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.behavior.mapper.YcxBehaviorSelectedMapper;
import com.bmh.project.evaluation.behavior.model.YcxBehaviorSelected;
import com.bmh.project.evaluation.behavior.service.YcxBehaviorSelectedService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * YcxBehaviorSelectedService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月15日 12:21:59
 */
@Service
public class YcxBehaviorSelectedServiceImpl extends BaseServiceImpl<YcxBehaviorSelected> implements YcxBehaviorSelectedService {
    @Resource
    private YcxBehaviorSelectedMapper ycxBehaviorSelectedMapper;

    @Override
    public void saveSelecteds(List<YcxBehaviorSelected> behaviorSelecteds, Integer recordId) {
        behaviorSelecteds.forEach(op -> {
            op.setRecordId(recordId);
            op.setCreateTime(new Date());
            op.setStatus(1);
            op.setUpdateTime(new Date());
        });
        this.insertList(behaviorSelecteds);
    }




}