let api = [];
api.push({
    alias: 'AbaAnalysisController',
    order: '1',
    link: '康复师端数据分析',
    desc: '康复师端数据分析',
    list: []
})
api[0].list.push({
    order: '1',
    desc: '获取回合制短期目标通过率折线图数据',
});
api[0].list.push({
    order: '2',
    desc: '获取回合制分析数据列表',
});
api[0].list.push({
    order: '3',
    desc: '获取试探项目小目标累计通过数量折线图数据',
});
api[0].list.push({
    order: '4',
    desc: '获取试探项目列表数据',
});
api[0].list.push({
    order: '5',
    desc: '获取分步骤列表数据',
});
api[0].list.push({
    order: '6',
    desc: '获取分步骤折线图数据',
});
api.push({
    alias: 'AbaCourseController',
    order: '2',
    link: 'aba-课程接口',
    desc: 'ABA-课程接口',
    list: []
})
api[1].list.push({
    order: '1',
    desc: '创建课程',
});
api[1].list.push({
    order: '2',
    desc: '获取课程详情',
});
api[1].list.push({
    order: '3',
    desc: '结束课程',
});
api[1].list.push({
    order: '4',
    desc: '判断当前是否正在上课',
});
api.push({
    alias: 'AbaCourseRecordController',
    order: '3',
    link: 'aba-课程记录接口',
    desc: 'ABA-课程记录接口',
    list: []
})
api[2].list.push({
    order: '1',
    desc: '增加记录',
});
api[2].list.push({
    order: '2',
    desc: '删除记录',
});
api[2].list.push({
    order: '3',
    desc: '更新备注',
});
api.push({
    alias: 'AbaCourseRemarkController',
    order: '4',
    link: 'aba-课程备注',
    desc: 'ABA-课程备注',
    list: []
})
api[3].list.push({
    order: '1',
    desc: '保存课程备注',
});
api[3].list.push({
    order: '2',
    desc: '获取课程备注',
});
api[3].list.push({
    order: '3',
    desc: '获取课程备注列表',
});
api[3].list.push({
    order: '4',
    desc: '督导老师督导相应课程',
});
api[3].list.push({
    order: '5',
    desc: '修改备注视频',
});
api[3].list.push({
    order: '6',
    desc: '获得机构相应的待读 数量',
});
api[3].list.push({
    order: '7',
    desc: '备注信息已读',
});
api[3].list.push({
    order: '8',
    desc: '批量已读',
});
api.push({
    alias: 'AbaDailyGoalDataController',
    order: '5',
    link: 'aba-每日数据接口',
    desc: 'ABA-每日数据接口',
    list: []
})
api[4].list.push({
    order: '1',
    desc: '获取每日数据',
});
api[4].list.push({
    order: '2',
    desc: '获取督导数据',
});
api[4].list.push({
    order: '3',
    desc: '获取指定月份有数据的日期',
});
api[4].list.push({
    order: '4',
    desc: '数据分析-小目标汇总',
});
api[4].list.push({
    order: '5',
    desc: '获取督导点击折线日期显示的数据详情和调整督导公用',
});
api.push({
    alias: 'AbaDailyInfoController',
    order: '6',
    link: 'aba-每日数据信息接口',
    desc: 'ABA-每日数据信息接口',
    list: []
})
api[5].list.push({
    order: '1',
    desc: '保存每日数据备注',
});
api[5].list.push({
    order: '2',
    desc: '获取每日信息备注',
});
api.push({
    alias: 'AbaDomainController',
    order: '7',
    link: 'aba-领域方向接口',
    desc: 'ABA-领域方向接口',
    list: []
})
api[6].list.push({
    order: '1',
    desc: '获取',
});
api.push({
    alias: 'AbaPlanController',
    order: '8',
    link: 'aba-计划接口',
    desc: 'ABA-计划接口',
    list: []
})
api[7].list.push({
    order: '1',
    desc: '获取计划基础信息',
});
api[7].list.push({
    order: '2',
    desc: '新增计划',
});
api[7].list.push({
    order: '3',
    desc: 'TODO：实践过程中有出现督导对孩子能力评估错误，孩子未学会的项目也给了通过，重新添加时又由于当前逻辑是已通过的项目不可再次添加，出现数据与实际的偏差。TODO：继续观察此类情况的出现频率，如果频率过高，系统可考虑增加项目重启功能，同时加强督导的通过标准修改计划 原来的有项目有id 的更新，都更新为最新，多短期目标的有Id的也更新，删除的界面单独调用接口删掉，增加的传id 传null 新增',
});
api[7].list.push({
    order: '4',
    desc: '调整计划信息',
});
api[7].list.push({
    order: '5',
    desc: '修改计划的项目最小回合数',
});
api[7].list.push({
    order: '6',
    desc: '删除计划',
});
api[7].list.push({
    order: '7',
    desc: '检查是否存在计划(不管状态是否正常)',
});
api[7].list.push({
    order: '8',
    desc: '重启计划',
});
api[7].list.push({
    order: '9',
    desc: '根据儿童ID获取计划详情(包含已通过和已挂起的项目)',
});
api[7].list.push({
    order: '10',
    desc: '根据计划ID获取计划详情(不包含已通过和已挂起的项目)',
});
api[7].list.push({
    order: '11',
    desc: '获取计划详情-备课(不包含已通过和已挂起的项目)',
});
api.push({
    alias: 'AbaPlanProjectController',
    order: '9',
    link: 'aba-计划项目接口',
    desc: 'ABA-计划项目接口',
    list: []
})
api[8].list.push({
    order: '1',
    desc: '教师端-修改计划项目短期目标的小目标',
});
api[8].list.push({
    order: '2',
    desc: '结束试探项目短期目标',
});
api[8].list.push({
    order: '3',
    desc: '获取儿童计划项目统计-按状态',
});
api[8].list.push({
    order: '4',
    desc: '获取儿童计划项目统计-按域',
});
api[8].list.push({
    order: '5',
    desc: '获取儿童计划项目统计-按通过项目',
});
api.push({
    alias: 'AbaPlanProjectGoalController',
    order: '10',
    link: 'aba-计划项目相应目标接口',
    desc: 'ABA-计划项目相应目标接口',
    list: []
})
api.push({
    alias: 'AbaPlanProjectHistoryController',
    order: '11',
    link: 'aba-计划项目历史',
    desc: 'ABA-计划项目历史',
    list: []
})
api[10].list.push({
    order: '1',
    desc: '获取计划项目更改历史按项目查看的角度不变，下面分记录类型，短期目标，辅助方式查看相应的历史变动记录',
});
api[10].list.push({
    order: '2',
    desc: '获取计划项目最新历史',
});
api[10].list.push({
    order: '3',
    desc: '获取计划项目近期修改历史；修改成项目下面挂短期目标的方式 和 计划项目显示短期目标分组显示的方式一致',
});
api.push({
    alias: 'AbaPrepController',
    order: '12',
    link: 'aba-备课接口',
    desc: 'ABA-备课接口',
    list: []
})
api[11].list.push({
    order: '1',
    desc: '保存备课记录',
});
api[11].list.push({
    order: '2',
    desc: '获取备课ID',
});
api[11].list.push({
    order: '3',
    desc: '批量获取备课记录详情',
});
api.push({
    alias: 'AbaPrepStatisticsController',
    order: '13',
    link: 'aba-备课统计接口',
    desc: 'ABA-备课统计接口',
    list: []
})
api[12].list.push({
    order: '1',
    desc: '【督导】获取备课统计老师列表',
});
api[12].list.push({
    order: '2',
    desc: '【督导】获取指定老师的统计记录',
});
api[12].list.push({
    order: '3',
    desc: '【教师】根据月份获取老师统计数据',
});
api.push({
    alias: 'AbaProjectController',
    order: '14',
    link: 'aba-项目接口',
    desc: 'ABA-项目接口',
    list: []
})
api[13].list.push({
    order: '1',
    desc: '根据领域ID获取项目集合',
});
api[13].list.push({
    order: '2',
    desc: '获取指定计划中可添加的项目列表',
});
api.push({
    alias: 'AbaProjectGoalController',
    order: '15',
    link: 'aba-项目短期目标',
    desc: 'ABA-项目短期目标',
    list: []
})
api[14].list.push({
    order: '1',
    desc: '获取项目的短期目标列表',
});
api[14].list.push({
    order: '2',
    desc: '获取项目的短期目标详情',
});
api.push({
    alias: 'AbaProjectRealiaController',
    order: '16',
    link: '项目相应教具大类(abaprojectrealia)表控制层',
    desc: '项目相应教具大类(AbaProjectRealia)表控制层',
    list: []
})
api[15].list.push({
    order: '1',
    desc: '查询项目下面的教具信息',
});
api[15].list.push({
    order: '2',
    desc: '查询项目是否有教具 0：没有 1：有',
});
api[15].list.push({
    order: '3',
    desc: '获得项目下面的3个教具信息',
});
api.push({
    alias: 'AbaProjectTargetController',
    order: '17',
    link: 'aba-项目小目标接口',
    desc: 'ABA-项目小目标接口',
    list: []
})
api[16].list.push({
    order: '1',
    desc: '获取项目的小目标列表',
});
api.push({
    alias: 'TestController',
    order: '18',
    link: '测试接口',
    desc: '测试接口',
    list: []
})
api[17].list.push({
    order: '1',
    desc: '每日数据',
});
api[17].list.push({
    order: '2',
    desc: '每日数据',
});
api[17].list.push({
    order: '3',
    desc: '按领域分析数据',
});
api.push({
    alias: 'AiAbaPlanProjectController',
    order: '19',
    link: '大模型生成aba康复疗程',
    desc: '大模型生成aba康复疗程',
    list: []
})
api[18].list.push({
    order: '1',
    desc: '根据儿童ID和评估ID一键生成康复计划',
});
api[18].list.push({
    order: '2',
    desc: '根据儿童id，评估id查询儿童信息后让大模型根据信息选择康复领域',
});
api[18].list.push({
    order: '3',
    desc: '根据儿童id，评估id查询儿童信息后让大模型根据信息选择康复项目',
});
api[18].list.push({
    order: '4',
    desc: '根据儿童id，评估id查询儿童信息后让大模型根据信息选择康复项目的短期目标',
});
api[18].list.push({
    order: '5',
    desc: '根据儿童id，评估id查询儿童信息后让大模型根据信息选择康复项目的短期目标的辅助方式和小目标',
});
api[18].list.push({
    order: '6',
    desc: '保存DeepSeek生成的LMT计划',
});
api[18].list.push({
    order: '7',
    desc: '数组参数缓存',
});
api.push({
    alias: 'AbaAnalysisDomainController',
    order: '20',
    link: 'aba按领域智能分析-接口',
    desc: 'ABA按领域智能分析-接口',
    list: []
})
api[19].list.push({
    order: '1',
    desc: '以领域维度统计数据',
});
api.push({
    alias: 'AnalysisChildController',
    order: '21',
    link: '分析模块-个案康复数据汇总接口',
    desc: '分析模块-个案康复数据汇总接口',
    list: []
})
api[20].list.push({
    order: '1',
    desc: '查询儿童康复数据',
});
api[20].list.push({
    order: '2',
    desc: '获取ABA通过项目数排行榜',
});
api.push({
    alias: 'AnalysisCourseController',
    order: '22',
    link: '分析模块-课程汇总接口',
    desc: '分析模块-课程汇总接口',
    list: []
})
api[21].list.push({
    order: '1',
    desc: '以机构维度统计数据',
});
api[21].list.push({
    order: '2',
    desc: '以康复师维度统计数据',
});
api[21].list.push({
    order: '3',
    desc: '以课程类型维度统计数据',
});
api[21].list.push({
    order: '4',
    desc: '获取课程明细(全平台)',
});
api[21].list.push({
    order: '5',
    desc: '查询机构的账号余额等',
});
api[21].list.push({
    order: '6',
    desc: '查询机构的账号费用明细',
});
api.push({
    alias: 'AnalysisDashboardController',
    order: '23',
    link: '分析模块-数据看板接口',
    desc: '分析模块-数据看板接口',
    list: []
})
api[22].list.push({
    order: '1',
    desc: '',
});
api.push({
    alias: 'AnalysisSupervisionController',
    order: '24',
    link: '分析模块-督导数据汇总接口',
    desc: '分析模块-督导数据汇总接口',
    list: []
})
api[23].list.push({
    order: '1',
    desc: '获取ABA综合项目督导数据',
});
api.push({
    alias: 'ChildrenBookController',
    order: '25',
    link: '',
    desc: '',
    list: []
})
api[24].list.push({
    order: '1',
    desc: '获取医生可预约的时间段',
});
api[24].list.push({
    order: '2',
    desc: '添加预约',
});
api[24].list.push({
    order: '3',
    desc: '获取预约信息',
});
api[24].list.push({
    order: '4',
    desc: '获取医师预约月历',
});
api[24].list.push({
    order: '5',
    desc: '获取医师每日预约',
});
api[24].list.push({
    order: '6',
    desc: '取消预约',
});
api[24].list.push({
    order: '7',
    desc: '获取患者列表',
});
api.push({
    alias: 'ChildrenEvaluatingController',
    order: '26',
    link: '',
    desc: '',
    list: []
})
api[25].list.push({
    order: '1',
    desc: '获取评测量表',
});
api[25].list.push({
    order: '2',
    desc: '开始评测',
});
api[25].list.push({
    order: '3',
    desc: '获取医生评测统计',
});
api[25].list.push({
    order: '4',
    desc: '获取儿童评测量表',
});
api[25].list.push({
    order: '5',
    desc: '检查量表是否全部完成评测',
});
api[25].list.push({
    order: '6',
    desc: '结束评测',
});
api[25].list.push({
    order: '7',
    desc: '获取近期评测列表',
});
api[25].list.push({
    order: '8',
    desc: '获取正在评测列表',
});
api[25].list.push({
    order: '9',
    desc: '获取历史评测列表',
});
api[25].list.push({
    order: '10',
    desc: '督导获取儿童评测列表',
});
api[25].list.push({
    order: '11',
    desc: '删除评估项目',
});
api.push({
    alias: 'QiNiuFileController',
    order: '27',
    link: '七牛接口',
    desc: '七牛接口',
    list: []
})
api[26].list.push({
    order: '1',
    desc: '获取上传所需的token',
});
api[26].list.push({
    order: '2',
    desc: '获取七牛域',
});
api[26].list.push({
    order: '3',
    desc: '上传图片',
});
api.push({
    alias: 'SysAppVersionController',
    order: '28',
    link: 'app版本接口',
    desc: 'APP版本接口',
    list: []
})
api[27].list.push({
    order: '1',
    desc: '检查更新',
});
api.push({
    alias: 'YcxProjectController',
    order: '29',
    link: '评测项目接口',
    desc: '评测项目接口',
    list: []
})
api[28].list.push({
    order: '1',
    desc: '根据分类获取评测项目列表',
});
api[28].list.push({
    order: '2',
    desc: '设置图片',
});
api[28].list.push({
    order: '3',
    desc: '',
});
api.push({
    alias: 'CozeController',
    order: '30',
    link: '扣子api',
    desc: '扣子API',
    list: []
})
api[29].list.push({
    order: '1',
    desc: '获取Oauth Access Tokenhttps://www.coze.cn/docs/developer_guides/oauth_jwt_channel',
});
api.push({
    alias: 'DashBoardController',
    order: '31',
    link: '数据大屏接口',
    desc: '数据大屏接口',
    list: []
})
api[30].list.push({
    order: '1',
    desc: '根据code 查询机构今日概况',
});
api[30].list.push({
    order: '2',
    desc: '根据code 查询数据概览',
});
api[30].list.push({
    order: '3',
    desc: '教学排行榜,进步排行榜,儿童问题领域分布,督导排行榜课,节量趋势',
});
api[30].list.push({
    order: '4',
    desc: '获取机构名称',
});
api[30].list.push({
    order: '5',
    desc: '入口校验',
});
api.push({
    alias: 'EaChildFlowController',
    order: '32',
    link: '影子老师-孩子流程接口',
    desc: '影子老师-孩子流程接口',
    list: []
})
api[31].list.push({
    order: '1',
    desc: '获取孩子一日流程列表',
});
api[31].list.push({
    order: '2',
    desc: '保存孩子流程',
});
api[31].list.push({
    order: '3',
    desc: '获取孩子一日流程详情',
});
api.push({
    alias: 'EaCourseController',
    order: '33',
    link: '影子老师-课程接口',
    desc: '影子老师-课程接口',
    list: []
})
api[32].list.push({
    order: '1',
    desc: '创建课程',
});
api[32].list.push({
    order: '2',
    desc: '获取课程流程列表（包含流程列表）',
});
api[32].list.push({
    order: '3',
    desc: '获取课程环节数据列表(包含记录列表)',
});
api[32].list.push({
    order: '4',
    desc: '结束课程',
});
api[32].list.push({
    order: '5',
    desc: '判断当前是否正在上课',
});
api.push({
    alias: 'EaCourseRecordController',
    order: '34',
    link: '影子老师-上课记录接口',
    desc: '影子老师-上课记录接口',
    list: []
})
api[33].list.push({
    order: '1',
    desc: '增加记录(选择相应的 0 未完成 1 符合 2 不符合 )',
});
api[33].list.push({
    order: '2',
    desc: '删除记录',
});
api[33].list.push({
    order: '3',
    desc: '更新备注',
});
api[33].list.push({
    order: '4',
    desc: '获取记录详情',
});
api[33].list.push({
    order: '5',
    desc: '获取记录列表(包含记录明细列表)',
});
api[33].list.push({
    order: '6',
    desc: '近14节课特需行为发生频次：近14节课中，每天每节课出现特需行为的次数',
});
api[33].list.push({
    order: '7',
    desc: '近14节课特需行为发生环节排行',
});
api.push({
    alias: 'EaCourseRecordDetailController',
    order: '35',
    link: '影子老师-上课相应环节不符合的记录明细接口',
    desc: '影子老师-上课相应环节不符合的记录明细接口',
    list: []
})
api[34].list.push({
    order: '1',
    desc: '增加不符合的记录明细',
});
api[34].list.push({
    order: '2',
    desc: '删除记录明细',
});
api[34].list.push({
    order: '3',
    desc: '更新记录明细备注',
});
api[34].list.push({
    order: '4',
    desc: '获取记录详情',
});
api.push({
    alias: 'EaCourseRemarkController',
    order: '36',
    link: '影子老师-课程备注',
    desc: '影子老师-课程备注',
    list: []
})
api[35].list.push({
    order: '1',
    desc: '保存课程备注',
});
api[35].list.push({
    order: '2',
    desc: '获取课程备注',
});
api.push({
    alias: 'EaDailyDataController',
    order: '37',
    link: '影子老师-每日数据接口',
    desc: '影子老师-每日数据接口',
    list: []
})
api[36].list.push({
    order: '1',
    desc: '获取每日数据',
});
api[36].list.push({
    order: '2',
    desc: '获取指定月份有数据的日期',
});
api.push({
    alias: 'EaNodeController',
    order: '38',
    link: '影子老师-流程模板接口',
    desc: '影子老师-流程模板接口',
    list: []
})
api[37].list.push({
    order: '1',
    desc: '获得相应年级的流程模板 供孩子选择使用',
});
api.push({
    alias: 'EaProblemController',
    order: '39',
    link: '影子老师-问题行为相关接口',
    desc: '影子老师-问题行为相关接口',
    list: []
})
api[38].list.push({
    order: '1',
    desc: '获取指定流程的问题行为列表',
});
api[38].list.push({
    order: '2',
    desc: '获取指定流程的问题行为原因分析列表',
});
api[38].list.push({
    order: '3',
    desc: '获取问题行为处理方法列表',
});
api.push({
    alias: 'EvIepChildrenController',
    order: '40',
    link: '评估儿童相关接口',
    desc: '评估儿童相关接口',
    list: []
})
api[39].list.push({
    order: '1',
    desc: '所有儿童列表',
});
api[39].list.push({
    order: '2',
    desc: '添加儿童个案',
});
api[39].list.push({
    order: '3',
    desc: '儿童个案列表',
});
api.push({
    alias: 'EvIepController',
    order: '41',
    link: '评估iep相关接口',
    desc: '评估IEP相关接口',
    list: []
})
api[40].list.push({
    order: '1',
    desc: '获取当前儿童最新IEP报告',
});
api[40].list.push({
    order: '2',
    desc: '儿童IEP分页列表',
});
api[40].list.push({
    order: '3',
    desc: 'AI根据评估信息生成IEP',
});
api[40].list.push({
    order: '4',
    desc: '根据id获取IEP信息',
});
api[40].list.push({
    order: '5',
    desc: '删除IEP',
});
api[40].list.push({
    order: '6',
    desc: '添加或修改IEP',
});
api.push({
    alias: 'EvResultController',
    order: '42',
    link: '评估接口',
    desc: '评估接口',
    list: []
})
api[41].list.push({
    order: '1',
    desc: '创建/修改评估',
});
api.push({
    alias: 'AbcController',
    order: '43',
    link: 'abc_自闭症儿童行为量表',
    desc: 'ABC 自闭症儿童行为量表',
    list: []
})
api[42].list.push({
    order: '1',
    desc: '获取问题列表',
});
api.push({
    alias: 'AdirController',
    order: '44',
    link: 'adi-r_孤独症诊断访谈量表',
    desc: 'ADI-R 孤独症诊断访谈量表',
    list: []
})
api[43].list.push({
    order: '1',
    desc: '根据类型获取题目列表',
});
api.push({
    alias: 'AdosgController',
    order: '45',
    link: 'ados-g_孤独症诊断观察量表',
    desc: 'ADOS-G 孤独症诊断观察量表',
    list: []
})
api[44].list.push({
    order: '1',
    desc: '根据类型获取问题',
});
api.push({
    alias: 'YcxAmeController',
    order: '46',
    link: 'ame_青少年忧郁情绪自我检视表',
    desc: 'AME 青少年忧郁情绪自我检视表',
    list: []
})
api[45].list.push({
    order: '1',
    desc: '获取问题列表',
});
api.push({
    alias: 'YcxBehaviorController',
    order: '47',
    link: '0-6岁儿童发育行为评估',
    desc: '0-6岁儿童发育行为评估',
    list: []
})
api[46].list.push({
    order: '1',
    desc: '根据类型获取题库',
});
api.push({
    alias: 'CabsController',
    order: '48',
    link: 'cabs_克氏孤独症行为量表',
    desc: 'CABS 克氏孤独症行为量表',
    list: []
})
api[47].list.push({
    order: '1',
    desc: '获取问题列表',
});
api.push({
    alias: 'CarsController',
    order: '49',
    link: 'cars_自闭症评定量表',
    desc: 'CARS 自闭症评定量表',
    list: []
})
api[48].list.push({
    order: '1',
    desc: '获取问题列表',
});
api.push({
    alias: 'YcxCbclController',
    order: '50',
    link: 'achenbanch儿童行为量表cbcl',
    desc: 'Achenbanch儿童行为量表CBCL',
    list: []
})
api[49].list.push({
    order: '1',
    desc: '根据类型获取问题',
});
api.push({
    alias: 'YcxCdccController',
    order: '51',
    link: 'cdcc-0-3岁婴幼儿发育量表',
    desc: 'CDCC-0-3岁婴幼儿发育量表',
    list: []
})
api[50].list.push({
    order: '1',
    desc: '根据类型获取问题',
});
api[50].list.push({
    order: '2',
    desc: '查询真实分',
});
api.push({
    alias: 'ChatController',
    order: '52',
    link: 'chat-23_孤独症早期筛查量表(一级筛查)',
    desc: 'chat-23 孤独症早期筛查量表(一级筛查)',
    list: []
})
api[51].list.push({
    order: '1',
    desc: '根据类型获取问题',
});
api.push({
    alias: 'YcxCmtController',
    order: '53',
    link: 'cmt_3-6岁儿童心理健康测试',
    desc: 'CMT 3-6岁儿童心理健康测试',
    list: []
})
api[52].list.push({
    order: '1',
    desc: '获取问题列表',
});
api.push({
    alias: 'YcxConnersPsqController',
    order: '54',
    link: 'conners儿童行为问卷父母用量表psq',
    desc: 'Conners儿童行为问卷父母用量表PSQ',
    list: []
})
api[53].list.push({
    order: '1',
    desc: '根据类型获取问题',
});
api.push({
    alias: 'YcxConnersTrsController',
    order: '55',
    link: 'conners儿童行为问卷教师用量表trs',
    desc: 'Conners儿童行为问卷教师用量表TRS',
    list: []
})
api[54].list.push({
    order: '1',
    desc: '根据类型获取问题',
});
api.push({
    alias: 'YcxCsiController',
    order: '56',
    link: '儿童感觉统合能力测评(3-12岁)',
    desc: '儿童感觉统合能力测评(3-12岁)',
    list: []
})
api[55].list.push({
    order: '1',
    desc: '根据类型获取问题',
});
api[55].list.push({
    order: '2',
    desc: '获取标准分',
});
api.push({
    alias: 'DdstController',
    order: '57',
    link: 'ddst_丹佛小儿只能发育筛选',
    desc: 'DDST 丹佛小儿只能发育筛选',
    list: []
})
api[56].list.push({
    order: '1',
    desc: '根据分类获取题库',
});
api.push({
    alias: 'DstController',
    order: '58',
    link: 'dst_0-6岁儿童发育筛选测验',
    desc: 'DST 0-6岁儿童发育筛选测验',
    list: []
})
api[57].list.push({
    order: '1',
    desc: '根据分类获取题库',
});
api[57].list.push({
    order: '2',
    desc: '获取真实分数',
});
api.push({
    alias: 'YcxEcaaController',
    order: '59',
    link: 'ecaa(early_childhood_admission_assessment)_幼儿入学评估',
    desc: 'ECAA(Early Childhood Admission Assessment) 幼儿入学评估',
    list: []
})
api[58].list.push({
    order: '1',
    desc: '根据分类获取题库',
});
api.push({
    alias: 'YcxEpqcController',
    order: '60',
    link: 'epqc_艾森克人格个性检测',
    desc: 'EPQC 艾森克人格个性检测',
    list: []
})
api[59].list.push({
    order: '1',
    desc: '按照类型查看问题',
});
api[59].list.push({
    order: '2',
    desc: '根据类型，年龄，性别，分数，查询对应的标准分',
});
api.push({
    alias: 'YcxGesellController',
    order: '61',
    link: 'gesell发育诊断量表（gds）4周~6岁',
    desc: 'Gesell发育诊断量表（GDS）4周~6岁',
    list: []
})
api[60].list.push({
    order: '1',
    desc: '根据分类获取题库',
});
api.push({
    alias: 'YcxHamdController',
    order: '62',
    link: 'hamd_汉密尔顿抑郁量表',
    desc: 'HAMD 汉密尔顿抑郁量表',
    list: []
})
api[61].list.push({
    order: '1',
    desc: '获取所有问题',
});
api.push({
    alias: 'YcxIatController',
    order: '63',
    link: 'iat_网络成瘾诊断量表',
    desc: 'IAT 网络成瘾诊断量表',
    list: []
})
api[62].list.push({
    order: '1',
    desc: '获取问题列表',
});
api[62].list.push({
    order: '2',
    desc: '保存选项答案',
});
api.push({
    alias: 'YcxInmaController',
    order: '64',
    link: 'inma神经运动检查20项',
    desc: 'INMA神经运动检查20项',
    list: []
})
api[63].list.push({
    order: '1',
    desc: '获取问题列表',
});
api.push({
    alias: 'YcxLmtPrelController',
    order: '65',
    link: 'lmt评估-初评表接口',
    desc: 'LMT评估-初评表接口',
    list: []
})
api[64].list.push({
    order: '1',
    desc: '获取问题列表',
});
api[64].list.push({
    order: '2',
    desc: '获取问题列表',
});
api.push({
    alias: 'YcxLmtPrelResultController',
    order: '66',
    link: 'lmt评估-初评表结果接口',
    desc: 'LMT评估-初评表结果接口',
    list: []
})
api[65].list.push({
    order: '1',
    desc: '保存结果-医生端',
});
api[65].list.push({
    order: '2',
    desc: '绑定评估记录ID',
});
api[65].list.push({
    order: '3',
    desc: '保存结果-家长端',
});
api[65].list.push({
    order: '4',
    desc: '根据儿童ID获取初评结果',
});
api[65].list.push({
    order: '5',
    desc: '根据儿童ID获取初评结果',
});
api[65].list.push({
    order: '6',
    desc: '根据儿童ID获取初评结果-家长端',
});
api.push({
    alias: 'YcxLmtProjectController',
    order: '67',
    link: 'lmt评估-评估项目接口',
    desc: 'LMT评估-评估项目接口',
    list: []
})
api[66].list.push({
    order: '1',
    desc: '获取评估内容',
});
api[66].list.push({
    order: '2',
    desc: '检查评估所处阶段',
});
api[66].list.push({
    order: '3',
    desc: '督导端-获取评估内容',
});
api[66].list.push({
    order: '4',
    desc: '新增项目',
});
api[66].list.push({
    order: '5',
    desc: '删除项目',
});
api[66].list.push({
    order: '6',
    desc: '检查该儿童是否存在评估结果',
});
api.push({
    alias: 'YcxLmtProjectRecordController',
    order: '68',
    link: 'lmt评估-评估项目记录接口',
    desc: 'LMT评估-评估项目记录接口',
    list: []
})
api[67].list.push({
    order: '1',
    desc: '增加记录',
});
api[67].list.push({
    order: '2',
    desc: '删除记录',
});
api.push({
    alias: 'YcxNbnaController',
    order: '69',
    link: 'nbna_新生儿20项行为神经评定心理量表',
    desc: 'NBNA 新生儿20项行为神经评定心理量表',
    list: []
})
api[68].list.push({
    order: '1',
    desc: '根据类型获取问题',
});
api.push({
    alias: 'YcxNyadController',
    order: '70',
    link: '8-18岁青少年气质量表',
    desc: '8-18岁青少年气质量表',
    list: []
})
api[69].list.push({
    order: '1',
    desc: '获取问题列表',
});
api.push({
    alias: 'YcxNylsController',
    order: '71',
    link: 'nyls_3-7岁儿童气质问卷',
    desc: 'NYLS 3-7岁儿童气质问卷',
    list: []
})
api[70].list.push({
    order: '1',
    desc: '根据类型获取问题',
});
api[70].list.push({
    order: '2',
    desc: '获取气质纬度得分',
});
api.push({
    alias: 'YcxPdmsController',
    order: '72',
    link: 'pdms-\\_peabody_运动发育量表',
    desc: 'PDMS-\\ peabody 运动发育量表',
    list: []
})
api[71].list.push({
    order: '1',
    desc: '根据类型查询问题',
});
api[71].list.push({
    order: '2',
    desc: '根据类型和原始分查询标准分',
});
api[71].list.push({
    order: '3',
    desc: '根据总运动之和转换为商数',
});
api[71].list.push({
    order: '4',
    desc: '根据类型和原始分数找出相当年龄',
});
api.push({
    alias: 'YcxPrsController',
    order: '73',
    link: '学习障碍筛查量表5-15岁(prs)',
    desc: '学习障碍筛查量表5-15岁(PRS)',
    list: []
})
api[72].list.push({
    order: '1',
    desc: '获取问题',
});
api.push({
    alias: 'YcxRutterPController',
    order: '74',
    link: 'rutter_儿童行为量表(父母问卷)',
    desc: 'Rutter 儿童行为量表(父母问卷)',
    list: []
})
api[73].list.push({
    order: '1',
    desc: '查询所有问题(父母问卷)',
});
api.push({
    alias: 'YcxRuttertController',
    order: '75',
    link: 'rutter_儿童行为量表(老师问卷)',
    desc: 'Rutter 儿童行为量表(老师问卷)',
    list: []
})
api[74].list.push({
    order: '1',
    desc: '查询所有问题(老师问卷)',
});
api.push({
    alias: 'YcxSnapController',
    order: '76',
    link: 'snap-iv注意缺陷多动障碍筛查量表',
    desc: 'SNAP-IV注意缺陷多动障碍筛查量表',
    list: []
})
api[75].list.push({
    order: '1',
    desc: '根据类型获取问题',
});
api.push({
    alias: 'YcxTasController',
    order: '77',
    link: 'tas_sarason考试焦虑量表',
    desc: 'TAS Sarason考试焦虑量表',
    list: []
})
api[76].list.push({
    order: '1',
    desc: '获取问题列表',
});
api.push({
    alias: 'YcxVbDomainController',
    order: '78',
    link: 'vb评估模版接口',
    desc: 'vb评估模版接口',
    list: []
})
api[77].list.push({
    order: '1',
    desc: '获取评估人列表',
});
api.push({
    alias: 'YcxVbResultController',
    order: '79',
    link: '评估结果接口',
    desc: '评估结果接口',
    list: []
})
api[78].list.push({
    order: '1',
    desc: 'VB评估保存或修改',
});
api[78].list.push({
    order: '2',
    desc: '创建VBMAPP评估报告',
});
api[78].list.push({
    order: '3',
    desc: '判断当前儿童今天是否已生成了VBMAPP报告',
});
api[78].list.push({
    order: '4',
    desc: 'vb机构评估历史列表',
});
api[78].list.push({
    order: '5',
    desc: 'vb儿童评估历史',
});
api[78].list.push({
    order: '6',
    desc: '评估详情',
});
api.push({
    alias: 'YcxVbResultDomainController',
    order: '80',
    link: '评估领域里程碑接口',
    desc: '评估领域里程碑接口',
    list: []
})
api[79].list.push({
    order: '1',
    desc: '获取评估16个领域模版',
});
api[79].list.push({
    order: '2',
    desc: '修改评估领域数据',
});
api.push({
    alias: 'YcxVbResultDomainMsController',
    order: '81',
    link: '评估领域分数计算',
    desc: '评估领域分数计算',
    list: []
})
api[80].list.push({
    order: '1',
    desc: '选中加分',
});
api[80].list.push({
    order: '2',
    desc: '取消选中加分',
});
api.push({
    alias: 'YcxVbResultRecordController',
    order: '82',
    link: '评估结果记录接口',
    desc: '评估结果记录接口',
    list: []
})
api[81].list.push({
    order: '1',
    desc: '增加记录',
});
api[81].list.push({
    order: '2',
    desc: '删除记录',
});
api.push({
    alias: 'VbMappController',
    order: '83',
    link: 'vb-mapp_接口',
    desc: 'VB-MAPP 接口',
    list: []
})
api[82].list.push({
    order: '1',
    desc: '获取领域及里程碑列表',
});
api[82].list.push({
    order: '2',
    desc: '获取快捷记录列表',
});
api[82].list.push({
    order: '3',
    desc: '获取测试题目',
});
api[82].list.push({
    order: '4',
    desc: '提交答案',
});
api[82].list.push({
    order: '5',
    desc: '获取答题记录列表',
});
api[82].list.push({
    order: '6',
    desc: '删除记录',
});
api[82].list.push({
    order: '7',
    desc: '更改分数',
});
api[82].list.push({
    order: '8',
    desc: '获取分数',
});
api.push({
    alias: 'YcxWeissController',
    order: '84',
    link: 'weiss功能缺陷量表',
    desc: 'Weiss功能缺陷量表',
    list: []
})
api[83].list.push({
    order: '1',
    desc: '根据类型获取问题',
});
api.push({
    alias: 'YcxBocsController',
    order: '85',
    link: 'y-bocs_耶鲁布朗强迫症严重程度量表',
    desc: 'Y-BOCS 耶鲁布朗强迫症严重程度量表',
    list: []
})
api[84].list.push({
    order: '1',
    desc: '获取所有问题',
});
api.push({
    alias: 'YcxYgtssController',
    order: '86',
    link: 'ygtss_耶鲁综合抽动症严重程序量表',
    desc: 'YGTSS 耶鲁综合抽动症严重程序量表',
    list: []
})
api[85].list.push({
    order: '1',
    desc: '根据类型获取问题',
});
api.push({
    alias: 'AbaHomeworkController',
    order: '87',
    link: 'aba-家庭作业接口',
    desc: 'ABA-家庭作业接口',
    list: []
})
api[86].list.push({
    order: '1',
    desc: '获取候选项目列表(该儿童已通过及正在进行的项目列表)',
});
api[86].list.push({
    order: '2',
    desc: '创建家庭作业',
});
api.push({
    alias: 'AbaHwRecordController',
    order: '88',
    link: 'aba-家庭作业记录接口',
    desc: 'ABA-家庭作业记录接口',
    list: []
})
api[87].list.push({
    order: '1',
    desc: '获取指定月份有数据的日期',
});
api[87].list.push({
    order: '2',
    desc: '获取每日数据',
});
api.push({
    alias: 'MccCourseController',
    order: '89',
    link: '音乐集体课-课程接口',
    desc: '音乐集体课-课程接口',
    list: []
})
api[88].list.push({
    order: '1',
    desc: '创建课程',
});
api[88].list.push({
    order: '2',
    desc: '获取课程详情',
});
api[88].list.push({
    order: '3',
    desc: '结束课程',
});
api[88].list.push({
    order: '4',
    desc: '获取课程人员列表',
});
api[88].list.push({
    order: '5',
    desc: '获取指定月份有课程的日期',
});
api[88].list.push({
    order: '6',
    desc: '获取课程列表',
});
api.push({
    alias: 'MccCourseRecordController',
    order: '90',
    link: '音乐集体课-课程记录接口',
    desc: '音乐集体课-课程记录接口',
    list: []
})
api[89].list.push({
    order: '1',
    desc: '增加记录',
});
api[89].list.push({
    order: '2',
    desc: '删除记录',
});
api[89].list.push({
    order: '3',
    desc: '更新备注',
});
api[89].list.push({
    order: '4',
    desc: '获取儿童每日数据',
});
api[89].list.push({
    order: '5',
    desc: '获取儿童近14日数据',
});
api[89].list.push({
    order: '6',
    desc: '获取儿童近14日数据的指定日期详情',
});
api.push({
    alias: 'MccCourseTargetController',
    order: '91',
    link: '音乐集体课-课程目标接口',
    desc: '音乐集体课-课程目标接口',
    list: []
})
api[90].list.push({
    order: '1',
    desc: '获取课程目标&记录列表',
});
api[90].list.push({
    order: '2',
    desc: '情况说明补充',
});
api.push({
    alias: 'MccLessonPlanController',
    order: '92',
    link: '音乐集体课-教案接口',
    desc: '音乐集体课-教案接口',
    list: []
})
api[91].list.push({
    order: '1',
    desc: '获取教案列表',
});
api[91].list.push({
    order: '2',
    desc: '获取教案详情',
});
api.push({
    alias: 'MccOrgLessonPlanController',
    order: '93',
    link: '音乐集体课-机构教案接口',
    desc: '音乐集体课-机构教案接口',
    list: []
})
api[92].list.push({
    order: '1',
    desc: '获取机构教案列表',
});
api[92].list.push({
    order: '2',
    desc: '获取老师的机构教案',
});
api[92].list.push({
    order: '3',
    desc: '保存教案',
});
api.push({
    alias: 'ChannelRecordController',
    order: '94',
    link: '特殊通道查看评测结果相关接口',
    desc: '特殊通道查看评测结果相关接口',
    list: []
})
api[93].list.push({
    order: '1',
    desc: '获取评测记录列表',
});
api[93].list.push({
    order: '2',
    desc: '',
});
api[93].list.push({
    order: '3',
    desc: '获取特殊通道评测记数',
});
api.push({
    alias: 'YcxChildrenRecordController',
    order: '95',
    link: '评估记录',
    desc: '评估记录',
    list: []
})
api[94].list.push({
    order: '1',
    desc: '创建测评记录',
});
api[94].list.push({
    order: '2',
    desc: '获取记录信息',
});
api[94].list.push({
    order: '3',
    desc: '修改建议',
});
api[94].list.push({
    order: '4',
    desc: '保存评测结果',
});
api[94].list.push({
    order: '5',
    desc: '查询指定孩子的评测历史',
});
api[94].list.push({
    order: '6',
    desc: '更新评测结果图片',
});
api[94].list.push({
    order: '7',
    desc: '根据记录Id获取题目及选中项',
});
api[94].list.push({
    order: '8',
    desc: '修改建议',
});
api[94].list.push({
    order: '9',
    desc: '保存评测结果',
});
api[94].list.push({
    order: '10',
    desc: '获取记录信息',
});
api.push({
    alias: 'ReportController',
    order: '96',
    link: '课程，儿童信息，评估报告历史记录',
    desc: '课程，儿童信息，评估报告历史记录',
    list: []
})
api[95].list.push({
    order: '1',
    desc: '根据儿童ID查询儿童信息',
});
api[95].list.push({
    order: '2',
    desc: '查询LMT评估历史',
});
api[95].list.push({
    order: '3',
    desc: '根据儿童ID和评估ID查询初评表选中结果',
});
api[95].list.push({
    order: '4',
    desc: '根据儿童ID查询综合能力课程记录',
});
api[95].list.push({
    order: '5',
    desc: '根据儿童ID查询言语训练课程记录',
});
api[95].list.push({
    order: '6',
    desc: '根据儿童ID查询融合教育(影子老师)课程列表',
});
api[95].list.push({
    order: '7',
    desc: '生成报告',
});
api.push({
    alias: 'SaasController',
    order: '97',
    link: '',
    desc: '',
    list: []
})
api[96].list.push({
    order: '1',
    desc: '获取可进入的机构列表',
});
api[96].list.push({
    order: '2',
    desc: '选择机构',
});
api[96].list.push({
    order: '3',
    desc: '获取ERP上需要在PAD上课的任务',
});
api[96].list.push({
    order: '4',
    desc: '检查预约排班情况',
});
api[96].list.push({
    order: '5',
    desc: '发送验证码短信',
});
api.push({
    alias: 'StActivityController',
    order: '98',
    link: '言语训练-活动接口',
    desc: '言语训练-活动接口',
    list: []
})
api[97].list.push({
    order: '1',
    desc: '根据项目ID获取活动列表',
});
api[97].list.push({
    order: '2',
    desc: '保存言语活动',
});
api.push({
    alias: 'StCourseController',
    order: '99',
    link: '言语训练-课程接口',
    desc: '言语训练-课程接口',
    list: []
})
api[98].list.push({
    order: '1',
    desc: '创建课程',
});
api[98].list.push({
    order: '2',
    desc: '结束课程',
});
api[98].list.push({
    order: '3',
    desc: '获取课程详情',
});
api[98].list.push({
    order: '4',
    desc: '获取课程基本信息',
});
api[98].list.push({
    order: '5',
    desc: '获取课程目标列表',
});
api[98].list.push({
    order: '6',
    desc: '获取课程进度',
});
api[98].list.push({
    order: '7',
    desc: '获取指定月份有课程的日期',
});
api[98].list.push({
    order: '8',
    desc: '获取指定日期的课程列表',
});
api.push({
    alias: 'StCourseRecordController',
    order: '100',
    link: '言语训练-记录接口',
    desc: '言语训练-记录接口',
    list: []
})
api[99].list.push({
    order: '1',
    desc: '保存记录',
});
api[99].list.push({
    order: '2',
    desc: '获取儿童近14节课统计数据',
});
api.push({
    alias: 'StCourseRemarkController',
    order: '101',
    link: '言语训练-课程备注',
    desc: '言语训练-课程备注',
    list: []
})
api[100].list.push({
    order: '1',
    desc: '保存课程备注',
});
api[100].list.push({
    order: '2',
    desc: '获取课程备注',
});
api[100].list.push({
    order: '3',
    desc: '根据课程ID获取课程备注',
});
api[100].list.push({
    order: '4',
    desc: '获取课程备注列表',
});
api[100].list.push({
    order: '5',
    desc: '督导老师督导相应课程',
});
api[100].list.push({
    order: '6',
    desc: '修改备注视频',
});
api[100].list.push({
    order: '7',
    desc: '获得机构相应的待读 数量',
});
api[100].list.push({
    order: '8',
    desc: '备注信息已读',
});
api[100].list.push({
    order: '9',
    desc: '批量已读',
});
api.push({
    alias: 'StDictController',
    order: '102',
    link: '言语训练-基础信息接口',
    desc: '言语训练-基础信息接口',
    list: []
})
api[101].list.push({
    order: '1',
    desc: '获取列表',
});
api.push({
    alias: 'StPlanActivityHistoryController',
    order: '103',
    link: '言语训练-计划调整历史接口',
    desc: '言语训练-计划调整历史接口',
    list: []
})
api[102].list.push({
    order: '1',
    desc: '获取计划项目更改历史',
});
api[102].list.push({
    order: '2',
    desc: '获取计划项目最新历史',
});
api[102].list.push({
    order: '3',
    desc: '获取计划项目近期修改历史',
});
api.push({
    alias: 'StPlanController',
    order: '104',
    link: '言语训练-计划接口',
    desc: '言语训练-计划接口',
    list: []
})
api[103].list.push({
    order: '1',
    desc: '新增计划',
});
api[103].list.push({
    order: '2',
    desc: '修改计划',
});
api[103].list.push({
    order: '3',
    desc: '删除计划',
});
api[103].list.push({
    order: '4',
    desc: '检查是否存在计划(不管状态是否正常)',
});
api[103].list.push({
    order: '5',
    desc: '重启计划',
});
api[103].list.push({
    order: '6',
    desc: '根据计划ID获取计划详情(不包含已通过和已挂起的项目)',
});
api[103].list.push({
    order: '7',
    desc: '获取计划下所有活动列表（包含已通过挂起的）',
});
api[103].list.push({
    order: '8',
    desc: '获取计划活动详情列表',
});
api.push({
    alias: 'SysSupervisorController',
    order: '105',
    link: '督导人员接口',
    desc: '督导人员接口',
    list: []
})
api[104].list.push({
    order: '1',
    desc: '获取督导人员的机构用户模拟身份',
});
api[104].list.push({
    order: '2',
    desc: '获取督导人员的督导范围',
});
api.push({
    alias: 'LoginController',
    order: '106',
    link: '登录接口',
    desc: '登录接口',
    list: []
})
api[105].list.push({
    order: '1',
    desc: '登录',
});
api.push({
    alias: 'SysAreaController',
    order: '107',
    link: '地区接口',
    desc: '地区接口',
    list: []
})
api[106].list.push({
    order: '1',
    desc: '获取同一pid的下属地区',
});
api.push({
    alias: 'SysDictController',
    order: '108',
    link: '字典表接口',
    desc: '字典表接口',
    list: []
})
api[107].list.push({
    order: '1',
    desc: '获取字典列表',
});
api.push({
    alias: 'SysMessageController',
    order: '109',
    link: '系统消息接口',
    desc: '系统消息接口',
    list: []
})
api[108].list.push({
    order: '1',
    desc: '获取我的消息列表',
});
api[108].list.push({
    order: '2',
    desc: '消息已读',
});
api[108].list.push({
    order: '3',
    desc: '批量已读',
});
api[108].list.push({
    order: '4',
    desc: '获取未读消息数量',
});
api.push({
    alias: 'SysOrgController',
    order: '110',
    link: '机构接口',
    desc: '机构接口',
    list: []
})
api[109].list.push({
    order: '1',
    desc: '获取机构列表',
});
api[109].list.push({
    order: '2',
    desc: '获取机构信息',
});
api[109].list.push({
    order: '3',
    desc: '家长端小程序开关',
});
api[109].list.push({
    order: '4',
    desc: '个案归属开关',
});
api.push({
    alias: 'SysParentController',
    order: '111',
    link: '(sysparent)表控制层',
    desc: '(SysParent)表控制层',
    list: []
})
api.push({
    alias: 'SysUserController',
    order: '112',
    link: '用户管理',
    desc: '用户管理',
    list: []
})
api[111].list.push({
    order: '1',
    desc: '获取机构用户下拉列表数据',
});
api[111].list.push({
    order: '2',
    desc: '音乐集体课程获取辅助老师列表',
});
api[111].list.push({
    order: '3',
    desc: '获取本机构用户列表',
});
api[111].list.push({
    order: '4',
    desc: '保存用户信息',
});
api[111].list.push({
    order: '5',
    desc: '重置密码',
});
api[111].list.push({
    order: '6',
    desc: '修改密码',
});
api[111].list.push({
    order: '7',
    desc: '修改用户状态',
});
api[111].list.push({
    order: '8',
    desc: '获取本机构医生列表',
});
api.push({
    alias: 'YcxChildrenController',
    order: '113',
    link: '孩子患者接口',
    desc: '孩子患者接口',
    list: []
})
api[112].list.push({
    order: '1',
    desc: '获取机构用户下拉列表数据',
});
api[112].list.push({
    order: '2',
    desc: '获取儿童信息',
});
api[112].list.push({
    order: '3',
    desc: '获取历史孩子',
});
api[112].list.push({
    order: '4',
    desc: '根据手机号获取儿童信息',
});
api[112].list.push({
    order: '5',
    desc: '',
});
api[112].list.push({
    order: '6',
    desc: '更新儿童孕周数',
});
api[112].list.push({
    order: '7',
    desc: '保存儿童信息',
});
api[112].list.push({
    order: '8',
    desc: 'ABA 更新儿童状态',
});
api[112].list.push({
    order: '9',
    desc: 'ABA模块使用的儿童列表',
});
api[112].list.push({
    order: '10',
    desc: '获取ABA备课儿童列表',
});
api[112].list.push({
    order: '11',
    desc: '影子老师模块使用的儿童列表年级类型(1 小班 2 中班 3 大班) 根据最新的有效的孩子一日流程获得',
});
api[112].list.push({
    order: '12',
    desc: '保存儿童信息问卷',
});
api[112].list.push({
    order: '13',
    desc: '获取儿童信息问卷',
});
api[112].list.push({
    order: '14',
    desc: '保存儿童信息问卷-家长端',
});
api[112].list.push({
    order: '15',
    desc: '音乐集体课模块使用的儿童列表',
});
api[112].list.push({
    order: '16',
    desc: '言语训练模块获取儿童列表',
});
api[112].list.push({
    order: '17',
    desc: '获取儿童列表',
});
api.push({
    alias: 'YcxChildrenLeaveRecordController',
    order: '114',
    link: '儿童请假记录接口',
    desc: '儿童请假记录接口',
    list: []
})
api[113].list.push({
    order: '1',
    desc: '获取指定月份的请假记录',
});
api.push({
    alias: 'dict',
    order: '115',
    link: 'dict_list',
    desc: '数据字典',
    list: []
})
document.onkeydown = keyDownSearch;
function keyDownSearch(e) {
    const theEvent = e;
    const code = theEvent.keyCode || theEvent.which || theEvent.charCode;
    if (code == 13) {
        const search = document.getElementById('search');
        const searchValue = search.value;
        let searchArr = [];
        for (let i = 0; i < api.length; i++) {
            let apiData = api[i];
            const desc = apiData.desc;
            if (desc.indexOf(searchValue) > -1) {
                searchArr.push({
                    order: apiData.order,
                    desc: apiData.desc,
                    link: apiData.link,
                    list: apiData.list
                });
            } else {
                let methodList = apiData.list || [];
                let methodListTemp = [];
                for (let j = 0; j < methodList.length; j++) {
                    const methodData = methodList[j];
                    const methodDesc = methodData.desc;
                    if (methodDesc.indexOf(searchValue) > -1) {
                        methodListTemp.push(methodData);
                        break;
                    }
                }
                if (methodListTemp.length > 0) {
                    const data = {
                        order: apiData.order,
                        desc: apiData.desc,
                        link: apiData.link,
                        list: methodListTemp
                    };
                    searchArr.push(data);
                }
            }
        }
        let html;
        if (searchValue == '') {
            const liClass = "";
            const display = "display: none";
            html = buildAccordion(api,liClass,display);
            document.getElementById('accordion').innerHTML = html;
        } else {
            const liClass = "open";
            const display = "display: block";
            html = buildAccordion(searchArr,liClass,display);
            document.getElementById('accordion').innerHTML = html;
        }
        const Accordion = function (el, multiple) {
            this.el = el || {};
            this.multiple = multiple || false;
            const links = this.el.find('.dd');
            links.on('click', {el: this.el, multiple: this.multiple}, this.dropdown);
        };
        Accordion.prototype.dropdown = function (e) {
            const $el = e.data.el;
            $this = $(this), $next = $this.next();
            $next.slideToggle();
            $this.parent().toggleClass('open');
            if (!e.data.multiple) {
                $el.find('.submenu').not($next).slideUp("20").parent().removeClass('open');
            }
        };
        new Accordion($('#accordion'), false);
    }
}

function buildAccordion(apiData, liClass, display) {
    let html = "";
    let doc;
    if (apiData.length > 0) {
        for (let j = 0; j < apiData.length; j++) {
            html += '<li class="'+liClass+'">';
            html += '<a class="dd" href="#_' + apiData[j].link + '">' + apiData[j].order + '.&nbsp;' + apiData[j].desc + '</a>';
            html += '<ul class="sectlevel2" style="'+display+'">';
            doc = apiData[j].list;
            for (let m = 0; m < doc.length; m++) {
                html += '<li><a href="#_' + apiData[j].order + '_' + doc[m].order + '_' + doc[m].desc + '">' + apiData[j].order + '.' + doc[m].order + '.&nbsp;' + doc[m].desc + '</a> </li>';
            }
            html += '</ul>';
            html += '</li>';
        }
    }
    return html;
}