<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.rutterp.mapper.YcxRutterParentMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.rutterp.model.YcxRutterParent">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.rutterp.model.YcxRutterParentOption" >
      <result column="rutterp_id" jdbcType="INTEGER" property="rutterpId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="value" jdbcType="VARCHAR" property="value" />
      <result column="score" jdbcType="VARCHAR" property="score" />
    </collection>

  </resultMap>
    <select id="getRutterP" resultMap="BaseResultMap">
      select rutter.*
        , op.rutterp_id
        , op.name
        , op.value
        , op.score
      from ycx_rutterp as rutter
      left join ycx_rutterp_option as op
      on
        op.rutterp_id = rutter.id
      order by
        rutter.order_num asc,
        rutter.id asc, op.value asc
    </select>
</mapper>