<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ea.mapper.EaCourseRecordMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ea.model.EaCourseRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="child_flow_id" jdbcType="INTEGER" property="childFlowId" />
    <result column="child_flow_node_id" jdbcType="INTEGER" property="childFlowNodeId" />
    <result column="child_flow_sub_node_id" jdbcType="INTEGER" property="childFlowSubNodeId" />
    <result column="target_done" jdbcType="INTEGER" property="targetDone" />
    <result column="problem_behavior_id" jdbcType="INTEGER" property="problemBehaviorId" />
    <result column="problem_behavior" jdbcType="VARCHAR" property="problemBehavior" />
    <result column="problem_reason_id" jdbcType="INTEGER" property="problemReasonId" />
    <result column="problem_reason" jdbcType="VARCHAR" property="problemReason" />
    <result column="problem_solution_id" jdbcType="INTEGER" property="problemSolutionId" />
    <result column="problem_solution" jdbcType="VARCHAR" property="problemSolution" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="urls" jdbcType="LONGVARCHAR" property="urls" />
  </resultMap>

  <resultMap id="RecordWithDetailResultMap" type="com.bmh.project.ea.vo.EaCourseRecordListVo">
      <id column="id" jdbcType="INTEGER" property="id" />
      <result column="course_id" property="courseId"/>
      <result column="target_done" property="targetDone"/>
      <result column="has_remark" property="hasRemark"/>

      <collection property="recordDetailList" ofType="com.bmh.project.ea.vo.EaCourseRecordDetailListVo">
        <id column="detail_id" property="id"/>
        <result column="problem_behavior_id" property="problemBehaviorId"/>
        <result column="problem_behavior" property="problemBehavior"/>
      </collection>

  </resultMap>

  <select id="getRecordWithDetailList" resultMap="RecordWithDetailResultMap">
    select eocr.id,
           eocr.course_id,
           eocr.target_done,
           detail.id as detail_id,
           detail.problem_behavior_id,
           detail.problem_behavior,
           case
             when eocr.id is null
               then null
             when eocr.id is not null and
                  (COALESCE(eocr.remark, eocr.problem_behavior, eocr.problem_reason,
                            eocr.problem_solution) is not null)
               then 1
             else 0
             end as has_remark
    from  ea_course_record eocr
           left join ea_course_record_detail detail
            on detail.record_id = eocr.id and  detail.status = 1
    where eocr.child_flow_node_id = #{childFlowNodeId}
      and eocr.child_flow_sub_node_id = #{childFlowsubNodeId}
      and eocr.course_id = #{courseId}
      and eocr.status = 1
    order by eocr.create_time
  </select>

    <select id="getFrequencyAnalysisList" resultType="com.bmh.project.ea.vo.EaChildFrequencyAnalysisShowVo">
        SELECT date(ec.start_time) as totalDate ,ec.org_id as orgId, ec.child_id as childId ,erd.id as detailId,
               ec.start_time as startTime,ec.end_time as endTime,er.id as recordId,
               erd.problem_behavior_id as problemBehaviorId,erd.problem_behavior as  problembehavior
        from  (
            SELECT ec.start_time,ec.end_time ,ec.id,ec.org_id, ec.child_id
            from ea_course ec
            where ec.`status`=2
            and ec.child_id = #{childId}
            order by date(ec.start_time) desc
            LIMIT 14
            ) as ec
                 LEFT JOIN ea_course_record er on ec.id = er.course_id and er.target_done=2 and er.status =1
                 LEFT JOIN ea_course_record_detail erd on er.id = erd.record_id and erd.status =1
        where  ec.child_id = #{childId}
    </select>

    <select id="getFlowSubAnalysisList" resultType="com.bmh.project.ea.vo.EaChildFlowSubAnalysisVo">
        select
            ec.org_id AS orgId,
            ec.child_id AS childId,
            IFNULL(sn.id,fsn.id) AS subNodeId,
            IFNULL(IFNULL(sn.full_name,fsn.full_name),'其他') AS fullName,
            count( erd.id ) AS FrequencyNum
        from (
                 SELECT ec.id,ec.org_id, ec.child_id
                 from ea_course ec
                 where ec.`status`=2
                   and ec.child_id = #{childId}
                 order by date(ec.start_time) desc
				LIMIT 14
				) as ec
       LEFT JOIN ea_course_record er on ec.id = er.course_id and er.target_done=2 and er.status =1
            LEFT JOIN ea_course_record_detail erd on er.id = erd.record_id and erd.status =1
            LEFT JOIN ea_child_flow_sub_node fsn on er.child_flow_sub_node_id = fsn.id and fsn.status =1
            LEFT JOIN ea_sub_node sn on fsn.sub_node_id = sn.id and sn.status =1
        group by ec.org_id, ec.child_id ,IFNULL(sn.id,fsn.id),IFNULL(sn.full_name,fsn.full_name)
        ORDER BY  count(erd.id) desc
    </select>



</mapper>