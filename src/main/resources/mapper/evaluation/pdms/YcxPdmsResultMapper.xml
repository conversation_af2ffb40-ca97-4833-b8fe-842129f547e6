<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.pdms.mapper.YcxPdmsResultMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.pdms.model.YcxPdmsResult">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="children_age" jdbcType="DECIMAL" property="childrenAge" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="result_TMQ" jdbcType="VARCHAR" property="resultTmq" />
    <result column="result_GMQ" jdbcType="VARCHAR" property="resultGmq" />
    <result column="result_FMQ" jdbcType="VARCHAR" property="resultFmq" />
    <result column="result_his" jdbcType="LONGVARCHAR" property="resultHis" />
    <result column="recome" jdbcType="VARCHAR" property="recome" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="result_column" jdbcType="LONGVARCHAR" property="resultColumn" />
  </resultMap>
</mapper>