package com.bmh.project.evaluation.vbmapp.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.vbmapp.dto.YcxVbmappSelectedDto;
import com.bmh.project.evaluation.vbmapp.model.YcxVbmappSelected;
import com.bmh.project.evaluation.vbmapp.vo.ChildNoteVo;

/**
 * ycx_vbmapp_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2022年11月22日 17:57:14
 */
public interface YcxVbmappSelectedService extends BaseService<YcxVbmappSelected> {
    /**
     * 提交答题记录
     * @param dto
     * @return
     */
    Integer submitAnswer(YcxVbmappSelectedDto dto);

    /**
     * 设置数据为无效
     * @param selectedId
     * @return
     */
    Integer deleteSelected(Integer selectedId);

    /**
     * 获取答题记录列表
     * @param subjectId
     * @param childId
     * @return
     */
    ChildNoteVo getChildNote(Integer subjectId, Integer childId);

}