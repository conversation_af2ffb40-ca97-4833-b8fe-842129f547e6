<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.nyls.mapper.YcxNylsMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.nyls.model.YcxNyls">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />


    <collection property="options" ofType="com.bmh.project.evaluation.nyls.model.YcxNylsOption" >
      <result column="nyls_id" jdbcType="INTEGER" property="nylsId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="value" jdbcType="VARCHAR" property="value" />
      <result column="score" jdbcType="VARCHAR" property="score" />
    </collection>

  </resultMap>



  <select id="getNylsByType" resultMap="BaseResultMap">
    select nyls.*
    , op.nyls_id
    , op.name
    , op.value
    , op.score
    from ycx_nyls as nyls
    inner join ycx_nyls_option as op
    on op.nyls_id = nyls.id
    where nyls.type = #{type}
    and nyls.status = 1
    order by nyls.order_num asc, op.id asc
  </select>
</mapper>