<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.conners.mapper.YcxConnersTrsSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.conners.model.YcxConnersTrsSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="trs_id" jdbcType="INTEGER" property="trsId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>