<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.hamd.mapper.YcxHamdSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.hamd.model.YcxHamdSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="hamd_id" jdbcType="INTEGER" property="hamdId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
    <select id="getSelecteds" resultMap="BaseResultMap">
      select sele.*,
      hamd.content,
      hamd.order_num,
      op.name as optionName
      from
        ycx_hamd_selected as sele
          inner join
        ycx_hamd as hamd
        on
          hamd.id = sele.hamd_id
          left join
        ycx_hamd_option as op
        on
            op.hamd_id = hamd.id
            and
            op.value = sele.answer
      where
        sele.record_id = 1
      order by hamd.order_num asc;
    </select>
</mapper>