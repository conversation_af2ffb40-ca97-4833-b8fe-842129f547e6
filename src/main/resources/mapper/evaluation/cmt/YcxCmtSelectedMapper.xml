<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.cmt.mapper.YcxCmtSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.cmt.model.YcxCmtSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="cmt_id" jdbcType="INTEGER" property="cmtId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

    <select id="getSelecteds" parameterType="Integer" resultMap="BaseResultMap">
      select
        sele.*,
        cmt.content,
        cmt.order_num,
        op.name as optionName
      from
        ycx_cmt_selected as sele
          inner join
        ycx_cmt as cmt
        on
          cmt.id = sele.cmt_id
          left join
        ycx_cmt_option as op
        on
            op.cmt_id = cmt.id
            and
            op.value = sele.answer
      where
        sele.record_id = #{recordId}
      order by cmt.order_num asc
    </select>
</mapper>