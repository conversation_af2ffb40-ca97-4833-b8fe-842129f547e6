<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.sta.mapper.YcxStaSelectedMapper">

    <resultMap id="YcxStaSelectedMap" type="com.bmh.project.evaluation.sta.model.YcxStaSelected">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="recordId" column="record_id" jdbcType="INTEGER"/>
        <result property="staId" column="sta_id" jdbcType="INTEGER"/>
        <result property="answer" column="answer" jdbcType="VARCHAR"/>
        <result property="score" column="score" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>
    <update id="allReset">
        update ycx_sta_selected set status = 0,update_time = #{updateTime} where record_id = #{recordId} and status = 1
    </update>
    <select id="getSelectedList" resultType="com.bmh.project.evaluation.sta.model.YcxStaSelected">
        SELECT
            sele.*,
            sta.content,
            sta.order_num,
            op.`name` AS optionName
        FROM
            ycx_sta_selected AS sele
                INNER JOIN ycx_sta AS sta ON sta.id = sele.sta_id
                LEFT JOIN ycx_sta_option AS op ON op.sta_id = sta.id
                AND op.`value` = sele.answer
        WHERE
            sele.record_id = #{recordId} AND sele.status = 1
        ORDER BY
            sta.order_num ASC
    </select>
    <select id="selectDetailByRecordIdList" resultType="com.bmh.project.evaluation.sta.vo.StaDetailVo">
        SELECT
            ys.id,
            ys.type,
            ys.tag,
            ys.content,
            yss.score choice,
            yss.record_id recordId
        FROM
            ycx_sta ys
                LEFT JOIN ycx_sta_selected yss ON ys.id = yss.sta_id
        WHERE
            yss.`status` = 1
          AND yss.record_id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY
            ys.id
    </select>

</mapper>

