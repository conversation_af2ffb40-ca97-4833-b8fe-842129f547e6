package com.bmh.project.evaluation.conners.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.conners.model.YcxConnersTrs;
import com.bmh.project.evaluation.conners.service.YcxConnersTrsService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Conners儿童行为问卷教师用量表TRS
 *
 * <AUTHOR>
 * @since 2021/7/10 15:03
 */
@RestController
@RequestMapping("/conners/trs")
public class YcxConnersTrsController {

    @Resource
    private YcxConnersTrsService trsService;

    /**
     * 根据类型获取问题
     * @param type 类型(1品行问题，2多动，3注意力不集中-被动， 4其他)
     * @return
     */
    @RequestMapping("/getTrsListByType")
    public Result<List<YcxConnersTrs>> getTrsListByType(Integer type){
        List<YcxConnersTrs> list = trsService.getListByType (type);
        return ResultUtil.success (list);
    }
}
