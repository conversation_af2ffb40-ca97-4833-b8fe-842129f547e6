<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaPlanProjectTemplateMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaPlanProjectTemplate">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_level" jdbcType="INTEGER" property="projectLevel" />
    <result column="long_goal" jdbcType="VARCHAR" property="longGoal" />
    <result column="short_goal_id" jdbcType="INTEGER" property="shortGoalId" />
    <result column="short_goal_name" jdbcType="VARCHAR" property="shortGoalName" />
    <result column="assist_method" jdbcType="VARCHAR" property="assistMethod" />
    <result column="target" jdbcType="LONGVARCHAR" property="target" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>