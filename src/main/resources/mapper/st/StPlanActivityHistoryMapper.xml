<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.st.mapper.StPlanActivityHistoryMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.st.model.StPlanActivityHistory">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="batch_no" jdbcType="INTEGER" property="batchNo" />
    <result column="plan_activity_id" jdbcType="INTEGER" property="planActivityId" />
    <result column="level_id" jdbcType="INTEGER" property="levelId" />
    <result column="level_name" jdbcType="VARCHAR" property="levelName" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="activity_id" jdbcType="INTEGER" property="activityId" />
    <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
    <result column="target_ids" jdbcType="VARCHAR" property="targetIds" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="getMaxBatchNo" resultType="java.lang.Integer">
    select
    max(batch_no)
    from st_plan_activity_history where plan_id = #{planId}
  </select>
</mapper>
