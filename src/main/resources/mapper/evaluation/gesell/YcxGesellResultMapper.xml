<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.gesell.mapper.YcxGesellResultMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.gesell.model.YcxGesellResult">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="children_age" jdbcType="DECIMAL" property="childrenAge" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="da_type1" jdbcType="VARCHAR" property="daType1" />
    <result column="da_type2" jdbcType="VARCHAR" property="daType2" />
    <result column="da_type3" jdbcType="VARCHAR" property="daType3" />
    <result column="da_type4" jdbcType="VARCHAR" property="daType4" />
    <result column="da_type5" jdbcType="VARCHAR" property="daType5" />
    <result column="da_avg" jdbcType="VARCHAR" property="daAvg" />
    <result column="dq_type1" jdbcType="VARCHAR" property="dqType1" />
    <result column="dq_type2" jdbcType="VARCHAR" property="dqType2" />
    <result column="dq_type3" jdbcType="VARCHAR" property="dqType3" />
    <result column="dq_type4" jdbcType="VARCHAR" property="dqType4" />
    <result column="dq_type5" jdbcType="VARCHAR" property="dqType5" />
    <result column="dq_avg" jdbcType="VARCHAR" property="dqAvg" />
    <result column="recom" jdbcType="VARCHAR" property="recom" />
    <result column="result_his" jdbcType="VARCHAR" property="resultHis" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>