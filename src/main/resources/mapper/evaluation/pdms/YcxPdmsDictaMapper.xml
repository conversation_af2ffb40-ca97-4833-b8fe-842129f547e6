<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.pdms.mapper.YcxPdmsDictaMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.pdms.model.YcxPdmsDicta">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="pdms_type" jdbcType="INTEGER" property="pdmsType" />
    <result column="min_age" jdbcType="DOUBLE" property="minAge" />
    <result column="max_age" jdbcType="DOUBLE" property="maxAge" />
    <result column="min_score" jdbcType="INTEGER" property="minScore" />
    <result column="max_score" jdbcType="INTEGER" property="maxScore" />
    <result column="real_score" jdbcType="INTEGER" property="realScore" />
  </resultMap>
  <select id="getRealScore" resultType="java.lang.Integer">
    SELECT dict.real_score
    FROM ycx_pdms_dict_a AS dict
    WHERE dict.pdms_type = #{type}
    AND (dict.min_age &lt;= #{age} AND dict.max_age &gt;= #{age})
    AND (dict.min_score &lt;= #{origScore} AND dict.max_score &gt;= #{origScore})
  </select>
</mapper>