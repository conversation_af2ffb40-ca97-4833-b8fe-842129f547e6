package com.bmh.project.evaluation.vbmapp.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.vbmapp.mapper.YcxVbmappSubjectBookMapper;
import com.bmh.project.evaluation.vbmapp.model.YcxVbmappSubjectBook;
import com.bmh.project.evaluation.vbmapp.model.YcxVbmappSubjectOption;
import com.bmh.project.evaluation.vbmapp.service.YcxVbmappSubjectBookService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.stream.Collectors;

/**
 * YcxVbmappSubjectBookService对应的实现类
 *
 * <AUTHOR>
 * @date 2022年11月29日 10:17:57
 */
@Service
public class YcxVbmappSubjectBookServiceImpl extends BaseServiceImpl<YcxVbmappSubjectBook> implements YcxVbmappSubjectBookService {
    @Resource
    private YcxVbmappSubjectBookMapper ycxVbmappSubjectBookMapper;

    @Override
    public List<YcxVbmappSubjectBook> getSubjectBooks(Integer subjectId) {
        Example example=new Example(YcxVbmappSubjectBook.class);
        example.createCriteria().andEqualTo("subjectId",subjectId);
        example.orderBy("orderNum").asc();
        List<YcxVbmappSubjectBook> list = this.selectByExample(example);
        return list;
    }
}