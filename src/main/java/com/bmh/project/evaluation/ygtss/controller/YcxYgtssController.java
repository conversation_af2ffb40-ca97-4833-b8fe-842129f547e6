package com.bmh.project.evaluation.ygtss.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.ygtss.model.YcxYgtss;
import com.bmh.project.evaluation.ygtss.service.YcxYgtssService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * YGTSS 耶鲁综合抽动症严重程序量表
 */
@RestController
@RequestMapping("/ygtss")
public class YcxYgtssController {

    @Autowired
    private YcxYgtssService ycxYgtssService;

    /**
     * 根据类型获取问题
     *
     * @param type 类型(1.抽动情况，2.抽动评价)
     * @return
     */
    @RequestMapping("/getYgtssByType")
    public Result<List<YcxYgtss>> getYgtssByType(Integer type) {
        List<YcxYgtss> ygtssList = ycxYgtssService.getYgtssByType(type);
        return ResultUtil.success(ygtssList);
    }
}
