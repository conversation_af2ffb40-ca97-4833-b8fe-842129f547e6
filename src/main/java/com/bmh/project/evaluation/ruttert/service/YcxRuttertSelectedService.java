package com.bmh.project.evaluation.ruttert.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.ruttert.model.YcxRuttertSelected;

import java.util.List;

/**
 * ycx_ruttert_selected表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年08月17日 09:39:00
 */
public interface YcxRuttertSelectedService extends BaseService<YcxRuttertSelected> {

    void saveSelected(List<YcxRuttertSelected> selecteds, Integer recordId);

    List<YcxRuttertSelected> getSelecteds(Integer recordId);
}