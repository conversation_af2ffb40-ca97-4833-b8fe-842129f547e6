<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ev.mapper.EvDomainProjectAnswerMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ev.model.EvDomainProjectAnswer">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_project_id" jdbcType="INTEGER" property="domainProjectId" />
    <result column="score" jdbcType="VARCHAR" property="score" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="no" jdbcType="INTEGER" property="no" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>
    <select id="selectByType" resultType="com.bmh.project.ev.model.EvDomainProjectAnswer">
      SELECT id,score,txt,domain_project_id AS domainProjectId FROM ev_domain_project_answer WHERE `status` = 1 AND is_delete = 0 AND type = #{type}
    </select>
    <select id="selectDomainTotalScore" resultType="java.util.Map">
        SELECT domain_id, SUM(max_score) AS domain_max_score_sum
        FROM (
                 SELECT domain_id, domain_project_id, MAX(score) AS max_score
                 FROM ev_domain_project_answer
                 WHERE type = 0 AND domain_id NOT IN (24,25)
                 GROUP BY domain_id, domain_project_id
             ) AS subquery
        GROUP BY domain_id;
    </select>
</mapper>
