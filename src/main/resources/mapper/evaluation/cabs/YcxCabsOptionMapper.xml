<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.cabs.mapper.YcxCabsOptionMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.cabs.model.YcxCabsOption">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="cabs_id" jdbcType="INTEGER" property="cabsId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="value" jdbcType="VARCHAR" property="value" />
  </resultMap>
</mapper>