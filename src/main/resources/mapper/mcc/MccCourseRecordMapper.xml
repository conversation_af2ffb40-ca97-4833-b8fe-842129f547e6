<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.mcc.mapper.MccCourseRecordMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.mcc.model.MccCourseRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="course_target_id" jdbcType="INTEGER" property="courseTargetId" />
    <result column="target_id" jdbcType="INTEGER" property="targetId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="target_done" jdbcType="INTEGER" property="targetDone" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="calcPassCount" resultType="java.util.Map">
    select SUM(IF(target_done = 1, 1, 0)) AS passCount,
    SUM(IF(target_done = 0, 1, 0)) AS failCount
    from mcc_course_record
    where course_target_id = #{courseTargetId}
    and status = 1
  </select>
</mapper>