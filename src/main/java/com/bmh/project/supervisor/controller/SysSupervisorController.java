package com.bmh.project.supervisor.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.supervisor.model.SysSupervisor;
import com.bmh.project.supervisor.model.SysSupervisorScope;
import com.bmh.project.supervisor.service.SysSupervisorScopeService;
import com.bmh.project.supervisor.service.SysSupervisorService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * 督导人员接口
 *
 * <AUTHOR>
 * @since 2023/7/24 11:59
 */
@RestController
@RequestMapping("/supervisor")
public class SysSupervisorController {

    @Resource
    private SysSupervisorService supervisorService;
    @Resource
    private SysSupervisorScopeService supervisorScopeService;

    /**
     * 获取督导人员的机构用户模拟身份
     * @param orgId 需要模拟的机构ID
     * @return
     */
    @GetMapping ("/getMockSysUser")
    public Result<?> getMockSysUser(Integer orgId){
        Map<String, Object> mockInfo = supervisorService.getMockSysUser (orgId);
        return ResultUtil.success (mockInfo);
    }

    /**
     * 获取督导人员的督导范围
     * @return
     */
    @GetMapping("/getSupervisorScopeList")
    public Result<?> getSupervisorScopeList (){
        if(SecurityUtil.getUserType () != 2){
            return ResultUtil.error ("当前用户禁止调用该接口");
        }
        List<SysSupervisorScope> sysSupervisorScopeList = supervisorScopeService.getListBySupervisorId (SecurityUtil.getUserId ());
        return ResultUtil.success (sysSupervisorScopeList);
    }

    /**
     * 修改密码
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     * @return
     */
    @RequestMapping ("/editPsw")
    public Result<?> resetPsw (String oldPassword, String newPassword) {
        SysSupervisor user = supervisorService.selectByPrimaryKey (SecurityUtil.getUserId ());
        boolean pswCheck = SecurityUtil.matchesPassword (oldPassword, user.getPassword ());
        if (! pswCheck) {
            return ResultUtil.error ("旧密码不正确");
        }
        user.setPassword (SecurityUtil.encryptPassword (newPassword));
        supervisorService.updateNotNull (user);
        return ResultUtil.success ();
    }
}
