<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.sta.mapper.YcxStaMapper">

    <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.sta.model.YcxSta">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="type" column="type" jdbcType="INTEGER"/>
        <result property="tag" column="tag" jdbcType="VARCHAR"/>
        <result property="content" column="content" jdbcType="VARCHAR"/>
        <result property="orderNum" column="order_num" jdbcType="INTEGER"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>

        <collection property="options" ofType="com.bmh.project.evaluation.sta.model.YcxStaOption" >
            <result column="optionId" property="id"/>
            <result column="sta_id" jdbcType="INTEGER" property="staId" />
            <result column="name" jdbcType="VARCHAR" property="name" />
            <result column="value" jdbcType="VARCHAR" property="value" />
            <result column="score" jdbcType="INTEGER" property="score" />
        </collection>
    </resultMap>
    <select id="selectList" resultMap="BaseResultMap">
        SELECT
            sta.*,
            op.id optionId,
            op.sta_id,
            op.`name`,
            op.`value`,
            op.score
        FROM
            ycx_sta AS sta
        LEFT JOIN ycx_sta_option AS op ON op.sta_id = sta.id
        WHERE sta.type = #{type}
        ORDER BY
            sta.order_num ASC,
            op.VALUE DESC,
            op.id ASC
    </select>
    <select id="getTagTotalScore" resultType="com.bmh.project.evaluation.sta.vo.StaTagVo">
        SELECT
            a.tag,
            SUM( a.score ) totalScore
        FROM
            (
                SELECT
                    ys.tag,
                    ys.order_num,
                    ys.id,
                    ys.content,
                    MAX( yso.score ) AS score
                FROM
                    ycx_sta ys
                        INNER JOIN ycx_sta_option yso ON ys.id = yso.sta_id
                WHERE
                    ys.type = 1
                GROUP BY
                    ys.tag,
                    ys.id
                ORDER BY
                    ys.order_num
            ) AS a
        GROUP BY
            a.tag
        ORDER BY
            a.order_num
    </select>
    <select id="getTwoDisTinctScore" resultType="java.lang.Integer">
        SELECT DISTINCT yso.score FROM ycx_sta ys INNER JOIN ycx_sta_option yso ON ys.id = yso.sta_id WHERE ys.type = 2 order by yso.score ASC
    </select>

</mapper>

