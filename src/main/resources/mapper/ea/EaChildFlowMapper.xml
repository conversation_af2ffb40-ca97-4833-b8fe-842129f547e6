<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ea.mapper.EaChildFlowMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ea.model.EaChildFlow">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="grade_type" jdbcType="INTEGER" property="gradeType" />
    <result column="flow_name" jdbcType="VARCHAR" property="flowName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

<resultMap id="ListResultMap" type="com.bmh.project.ea.vo.EaChildFlowVo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="grade_type" jdbcType="INTEGER" property="gradeType" />
    <result column="flow_name" jdbcType="VARCHAR" property="flowName" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />

    <result column="lastCourseTime" jdbcType="TIMESTAMP" property="lastCourseTime" />
</resultMap>

  <select id="getListByChildId" resultMap="ListResultMap">
      select eof.id,
             eof.org_id,
             eof.child_id,
             eof.grade_type,
             eof.flow_name,
             eof.status,
             eof.create_user,
             eof.create_time,
             eof.update_time,
             eof.update_user,
             max(ec.create_time) as lastCourseTime
      from ea_child_flow eof
               left join ea_course ec on ec.child_flow_id = eof.id
      where eof.child_id = #{childId}
      group by eof.id
      order by eof.create_time desc
  </select>
    <select id="getLastFlowId" resultType="java.lang.Integer">
        select id from ea_child_flow where child_id = #{childId} order by create_time desc limit 1
    </select>
    <select id="selectEndSupervisor" resultType="java.lang.Integer">
        SELECT
        ecf.child_id
        FROM
        ea_child_flow ecf
        INNER JOIN (SELECT child_id, MAX(create_time) AS create_time
        FROM ea_child_flow
        WHERE child_id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        GROUP BY child_id) e
        ON ecf.child_id = e.child_id
        AND ecf.create_time = e.create_time
    </select>

    <update id="closeChildFlow">
      update ea_child_flow set status = 0 where  child_id = #{childId} and status = 1
    </update>
</mapper>
