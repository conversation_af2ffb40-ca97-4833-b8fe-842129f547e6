package com.bmh.project.evaluation.conners.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.conners.mapper.YcxConnersPsqResultMapper;
import com.bmh.project.evaluation.conners.model.YcxConnersPsqResult;
import com.bmh.project.evaluation.conners.model.YcxConnersPsqSelected;
import com.bmh.project.evaluation.conners.service.YcxConnersPsqResultService;
import com.bmh.project.evaluation.conners.service.YcxConnersPsqSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxConnersPsqResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年07月10日 15:23:32
 */
@Service("21")
public class YcxConnersPsqResultServiceImpl extends BaseServiceImpl<YcxConnersPsqResult> implements YcxConnersPsqResultService, YcxResultService<YcxConnersPsqResult> {
    @Resource
    private YcxConnersPsqResultMapper ycxConnersPsqResultMapper;
    @Resource
    private YcxConnersPsqSelectedService psqSelectedService;

    /**
     * 保存结果
     *
     * @param childrenRecord
     */
    @Override
    public void saveResult (YcxChildrenRecord childrenRecord) {
        YcxConnersPsqResult result = JSONUtil.toBean (childrenRecord.getResultStr (), YcxConnersPsqResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge());
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setCreateTime(new Date ());
        ycxConnersPsqResultMapper.insert(result);

        List<YcxConnersPsqSelected> selecteds = result.getSelecteds ();
        psqSelectedService.saveSelecteds(selecteds, result.getRecordId());
    }

    /**
     * 获取评测结果
     *
     * @param recordId 记录ID
     * @return
     */
    @Override
    public YcxConnersPsqResult getResult (Integer recordId) {
        Example example = new Example(YcxConnersPsqResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxConnersPsqResult> list = ycxConnersPsqResultMapper.selectByExample(example);
        return CollectionUtil.isNotEmpty (list)?list.get(0):null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxConnersPsqResult result = new YcxConnersPsqResult ();
        result.setRecom (recom);
        Example example = new Example (YcxConnersPsqResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxConnersPsqResultMapper.updateByExampleSelective (result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}