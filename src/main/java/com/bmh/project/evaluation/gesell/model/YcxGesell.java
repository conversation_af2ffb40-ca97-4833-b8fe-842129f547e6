package com.bmh.project.evaluation.gesell.model;

import lombok.Data;

import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@Data
@Table(name = "ycx_gesell")
public class YcxGesell implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1适应性行为，2大运动，3精细动作，4语言，5个人-社会性行为)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 标题
     */
    @Column(name = "title")
    private String title;

    /**
     * 提示（1H 0O）
     */
    @Column(name = "tips")
    private String tips;

    /**
     * 特殊说明
     */
    @Column(name = "special")
    private String special;

    /**
     * 月
     */
    @Column(name = "month")
    private Integer month;

    /**
     * 内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 示例图片
     */
    @Column(name = "example_img")
    private String exampleImg;

    /**
     * 道具
     */
    @Column(name = "props")
    private String props;

    /**
     * 状态(0无效，1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}