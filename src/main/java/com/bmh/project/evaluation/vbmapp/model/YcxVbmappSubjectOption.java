package com.bmh.project.evaluation.vbmapp.model;

import java.io.Serializable;
import java.util.List;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_vbmapp_subject_option")
public class YcxVbmappSubjectOption implements Serializable {
    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 题目Id
     */
    @Column(name = "subject_id")
    private Integer subjectId;

    /**
     * 父级id
     */
    @Column(name = "parent_id")
    private Integer parentId;

    /**
     * 关键字
     */
    @Column(name = "keyword")
    private String keyword;

    /**
     * 排序号
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 是否默认（0否，1是）
     */
    @Column(name = "is_default")
    private Integer isDefault;

    /**
     * 是否快捷方式（0否，1是）
     */
    @Column(name = "is_quick")
    private Integer isQuick;

    /**
     * 子选项
     */
    @Transient
    private List<YcxVbmappSubjectOption> children;

    private static final long serialVersionUID = 1L;
}