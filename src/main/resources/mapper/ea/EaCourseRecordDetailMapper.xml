<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ea.mapper.EaCourseRecordDetailMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ea.model.EaCourseRecordDetail">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="child_flow_id" jdbcType="INTEGER" property="childFlowId" />
    <result column="child_flow_node_id" jdbcType="INTEGER" property="childFlowNodeId" />
    <result column="child_flow_sub_node_id" jdbcType="INTEGER" property="childFlowSubNodeId" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="problem_behavior_id" jdbcType="INTEGER" property="problemBehaviorId" />
    <result column="problem_behavior" jdbcType="VARCHAR" property="problemBehavior" />
    <result column="problem_reason_id" jdbcType="INTEGER" property="problemReasonId" />
    <result column="problem_reason" jdbcType="VARCHAR" property="problemReason" />
    <result column="problem_solution_id" jdbcType="INTEGER" property="problemSolutionId" />
    <result column="problem_solution" jdbcType="VARCHAR" property="problemSolution" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="urls" jdbcType="LONGVARCHAR" property="urls" />
  </resultMap>
</mapper>