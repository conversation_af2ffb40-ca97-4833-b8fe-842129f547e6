<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.common.mapper.SysAppVersionMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.common.model.SysAppVersion">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="version_code" jdbcType="INTEGER" property="versionCode" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="is_force" jdbcType="INTEGER" property="isForce" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <select id="getMaxVersion" resultMap="BaseResultMap">
    select *
    from sys_app_version
    where app_id=#{appId}
    and status = 1
    order by create_time desc
    limit 1
  </select>

  <select id="getLaterVersionList" resultMap="BaseResultMap">
      select *
      from sys_app_version
      where app_id = #{appId}
        and version_code > #{currentVersionCode}
        and status = 1
      order by create_time desc
  </select>
</mapper>
