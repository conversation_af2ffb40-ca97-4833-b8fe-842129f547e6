package com.bmh.project.evaluation.ygtss.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.ygtss.mapper.YcxYgtssMapper;
import com.bmh.project.evaluation.ygtss.model.YcxYgtss;
import com.bmh.project.evaluation.ygtss.service.YcxYgtssService;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxYgtssService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月13日 13:48:54
 */
@Service
public class YcxYgtssServiceImpl extends BaseServiceImpl<YcxYgtss> implements YcxYgtssService {
    @Resource
    private YcxYgtssMapper ycxYgtssMapper;

    /**
     * 根据类型获取问题
     *
     * @param type 类型(1.抽动情况，2.抽动评价)
     * @return
     */
    @Override
    public List<YcxYgtss> getYgtssByType(Integer type) {
            List<YcxYgtss> list = ycxYgtssMapper.getYgtssByType(type, 1, null);
            for (YcxYgtss ycxYgtss : list) {
                List<YcxYgtss> childrenList = ycxYgtssMapper.getYgtssByType(type, 2, ycxYgtss.getId());
                ycxYgtss.setChildygtss(childrenList);
            }
            return list;
    }
}