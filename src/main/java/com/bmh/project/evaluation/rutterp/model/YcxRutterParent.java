package com.bmh.project.evaluation.rutterp.model;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_rutterp")
public class YcxRutterParent implements Serializable {
    /**
     * 主键id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 所属违纪行为(0.A违纪行为 1.N神经症行为)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 题目内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 排序
     */
    @Column(name = "order_num")
    private Integer orderNum;

    /**
     * 状态(0.无效 1.有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "created_time")
    private Date createdTime;

    private static final long serialVersionUID = 1L;

    /**
     * 选项
     */
    @Transient
    private List<YcxRutterParentOption> options;
}