package com.bmh.project.evaluation.vbmapp.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.vbmapp.model.YcxVbmappResult;

import java.math.BigDecimal;

/**
 * ycx_vbmapp_result表对应的Service接口
 *
 * <AUTHOR>
 * @date 2022年11月22日 17:57:14
 */
public interface YcxVbmappResultService extends BaseService<YcxVbmappResult> {
    Integer changeScore(Integer subjectId, Integer childId, BigDecimal score);

    String getScore(Integer subjectId, Integer childId);
}