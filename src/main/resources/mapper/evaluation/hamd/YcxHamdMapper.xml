<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.hamd.mapper.YcxHamdMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.hamd.model.YcxHamd">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.hamd.model.YcxHamdOption" >
      <result column="hamd_id" jdbcType="INTEGER" property="hamdId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="value" jdbcType="VARCHAR" property="value" />
      <result column="score" jdbcType="VARCHAR" property="score" />
    </collection>
  </resultMap>
  <select id="getHamd" resultMap="BaseResultMap">
    select hamd.*
        , op.hamd_id
        , op.name
        , op.value
        , op.score
      from ycx_hamd as hamd
      left join ycx_hamd_option as op
      on
        op.hamd_id = hamd.id
      order by
        hamd.order_num asc,
        hamd.id asc,op.score asc
  </select>
</mapper>