<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.org.mapper.SysOrgConfigMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.org.model.SysOrgConfig">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="aba_course_price" jdbcType="DECIMAL" property="abaCoursePrice" />
    <result column="st_course_price" jdbcType="DECIMAL" property="stCoursePrice" />
    <result column="mcc_course_price" jdbcType="DECIMAL" property="mccCoursePrice" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectByOrgId" resultMap="BaseResultMap">
    select * from sys_org_config where org_id = #{orgId}
  </select>
</mapper>
