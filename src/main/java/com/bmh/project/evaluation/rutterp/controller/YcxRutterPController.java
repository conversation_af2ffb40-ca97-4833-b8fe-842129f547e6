package com.bmh.project.evaluation.rutterp.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.rutterp.model.YcxRutterParent;
import com.bmh.project.evaluation.rutterp.service.YcxRutterParentService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Rutter 儿童行为量表(父母问卷)
 */
@RestController
@RequestMapping("/rutterp")
public class YcxRutterPController {

    @Resource
    private YcxRutterParentService rutterParentService;


    /**
     * 查询所有问题(父母问卷)
     * @return
     */
    @RequestMapping("/getRutterP")
    public Result<List<YcxRutterParent>> getRutterP(){
        List<YcxRutterParent> rutterParentList = rutterParentService.getRutterP();
        return ResultUtil.success(rutterParentList);
    }
}
