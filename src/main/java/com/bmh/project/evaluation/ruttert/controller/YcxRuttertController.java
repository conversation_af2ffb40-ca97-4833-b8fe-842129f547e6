package com.bmh.project.evaluation.ruttert.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.ruttert.model.YcxRuttert;
import com.bmh.project.evaluation.ruttert.service.YcxRuttertService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Rutter 儿童行为量表(老师问卷)
 */
@RestController
@RequestMapping("/ruttert")
public class YcxRuttertController {

    @Resource
    private YcxRuttertService ycxRuttertService;

    /**
     * 查询所有问题(老师问卷)
     * @return
     */
    @RequestMapping("/getRuttert")
    public Result<List<YcxRuttert>> getRutter(){
        List<YcxRuttert> ruttertList = ycxRuttertService.getRutter();
        return ResultUtil.success(ruttertList);
    }
}
