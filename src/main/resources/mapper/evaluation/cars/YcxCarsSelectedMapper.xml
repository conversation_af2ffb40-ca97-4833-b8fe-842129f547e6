<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.cars.mapper.YcxCarsSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.cars.model.YcxCarsSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="cars_id" jdbcType="INTEGER" property="carsId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />

    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="optionName" jdbcType="VARCHAR" property="optionName" />

  </resultMap>


<!--  List<YcxCarsSelected> getSelecteds(Integer recordId);-->
  <select id="getSelecteds" parameterType="Integer" resultMap="BaseResultMap" >
    select sele.*
    , cars.content
    , cars.order_num
    , op.name as optionName
    from ycx_cars_selected as sele
    inner join ycx_cars as cars
    on cars.id = sele.cars_id
    left join ycx_cars_option as op
    on op.cars_id = cars.id
    and op.value = sele.answer
    where sele.record_id = #{recordId}
    order by cars.order_num asc
  </select>

</mapper>