<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.chat.mapper.YcxChatOptionMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.chat.model.YcxChatOption">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="chat_id" jdbcType="INTEGER" property="chatId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="value" jdbcType="VARCHAR" property="value" />
  </resultMap>
</mapper>