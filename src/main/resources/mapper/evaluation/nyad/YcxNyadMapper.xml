<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.nyad.mapper.YcxNyadMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.nyad.model.YcxNyad">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.nyad.model.YcxNyadOption" >
      <result column="nyad_id" jdbcType="INTEGER" property="nyadId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="value" jdbcType="VARCHAR" property="value" />
      <result column="score" jdbcType="VARCHAR" property="score" />
    </collection>

  </resultMap>
  <select id="getList" resultMap="BaseResultMap">
      select
        nyad.*,
        op.nyad_id,
        op.name,
        op.value,
        op.score
      from
        ycx_nyad as nyad
          left join
        ycx_nyad_option as op
        on
          op.nyad_id = nyad.id
      order by
        nyad.order_num asc,
        nyad.id asc,op.value asc
    </select>
</mapper>