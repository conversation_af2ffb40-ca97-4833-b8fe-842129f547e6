package com.bmh.project.evaluation.ybocs.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.ybocs.mapper.YcxYbocsMapper;
import com.bmh.project.evaluation.ybocs.model.YcxYbocs;
import com.bmh.project.evaluation.ybocs.service.YcxYbocsService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxYbocsService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月17日 11:17:04
 */
@Service
public class YcxYbocsServiceImpl extends BaseServiceImpl<YcxYbocs> implements YcxYbocsService {
    @Resource
    private YcxYbocsMapper ycxYbocsMapper;

    /**
     * 获取所有问题
     * @return
     */
    @Override
    public List<YcxYbocs> getYbocs() {
        return ycxYbocsMapper.getYbocs();
    }
}