<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ea.mapper.EaChildFlowNodeMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ea.model.EaChildFlowNode">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_flow_id" jdbcType="INTEGER" property="childFlowId" />
    <result column="node_id" jdbcType="INTEGER" property="nodeId" />
    <result column="icon" jdbcType="VARCHAR" property="icon"/>
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="start_time" jdbcType="TIME" property="startTime" />
    <result column="end_time" jdbcType="TIME" property="endTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <resultMap id="FullListResultMap" type="com.bmh.project.ea.vo.EaChildFlowNodeVo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_flow_id" jdbcType="INTEGER" property="childFlowId" />
    <result column="node_id" jdbcType="INTEGER" property="nodeId" />
    <result column="icon" jdbcType="VARCHAR" property="icon"/>
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="start_time" jdbcType="TIME" property="startTime" />
    <result column="end_time" jdbcType="TIME" property="endTime" />

    <collection property="subNodeList" ofType="com.bmh.project.ea.model.EaChildFlowSubNode">
      <id column="org_flow_sub_node_id" property="id"/>
      <result column="org_id" property="orgId"/>
      <result column="child_flow_id" property="childFlowId"/>
      <result column="child_flow_node_id" property="childFlowNodeId"/>
      <result column="node_id" property="nodeId"/>
      <result column="sub_node_id" property="subNodeId"/>
      <result column="child_flow_sub_node_name" property="name"/>
      <result column="standard" property="standard"/>
      <result column="order_num" property="orderNum"/>
    </collection>
  </resultMap>
  <select id="getFullListByChildFlowId" resultMap="FullListResultMap">
      select eofn.id,
             eofn.org_id,
             eofn.child_flow_id,
             eofn.node_id,
             eofn.icon,
             eofn.name,
             eofn.start_time,
             eofn.end_time,

             eofsn.id as org_flow_sub_node_id,
             eofsn.org_id,
             eofsn.child_flow_id,
             eofsn.child_flow_node_id,
             eofsn.node_id,
             eofsn.sub_node_id,
             eofsn.name as child_flow_sub_node_name,
             eofsn.standard,
             eofsn.order_num
      from ea_child_flow_node eofn
               left join ea_child_flow_sub_node eofsn on eofn.id = eofsn.child_flow_node_id and eofsn.status = 1
      where eofn.status = 1
          and eofn.child_flow_id = #{childFlowId}
      order by eofn.start_time, eofsn.order_num
  </select>
</mapper>