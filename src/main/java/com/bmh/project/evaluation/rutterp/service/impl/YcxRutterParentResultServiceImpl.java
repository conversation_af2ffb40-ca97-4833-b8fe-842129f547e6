package com.bmh.project.evaluation.rutterp.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.record.model.YcxChildrenRecord;
import com.bmh.project.evaluation.rutterp.mapper.YcxRutterParentResultMapper;
import com.bmh.project.evaluation.rutterp.model.YcxRutterParentResult;
import com.bmh.project.evaluation.rutterp.service.YcxRutterParentResultService;
import javax.annotation.Resource;

import com.bmh.project.evaluation.rutterp.service.YcxRutterParentSelectedService;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;

/**
 * YcxRutterParentResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月16日 18:13:51
 */
@Service("38")
public class YcxRutterParentResultServiceImpl extends BaseServiceImpl<YcxRutterParentResult> implements YcxRutterParentResultService , YcxResultService<YcxRutterParentResult> {
    @Resource
    private YcxRutterParentResultMapper ycxRutterParentResultMapper;
    @Resource
    private YcxRutterParentSelectedService selectedService;

    /**
     * 保存评测结果
     * @param childrenRecord 参数
     */
    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxRutterParentResult result = JSONUtil.toBean(childrenRecord.getResultStr(), YcxRutterParentResult.class);
        result.setRecordId(childrenRecord.getId());
        result.setChildrenAge(childrenRecord.getChildrenAge());
        result.setChildrenId(childrenRecord.getChildrenId());
        result.setOrgId(childrenRecord.getOrgId());
        result.setOperatorName(SecurityUtil.getNickName());
        result.setOperatorId(SecurityUtil.getUserId());
        result.setCreatedTime(new Date());
        ycxRutterParentResultMapper.insert(result);

        selectedService.saveSelected(result.getSelecteds(),result.getRecordId());
    }

    /**
     * 获取评测结果
     * @param recordId 评测记录ID
     * @return
     */
    @Override
    public YcxRutterParentResult getResult(Integer recordId) {
        Example example = new Example(YcxRutterParentResult.class);
        example.createCriteria().andEqualTo("recordId",recordId);
        List<YcxRutterParentResult> list = this.selectByExample(example);
        if (list != null && list.size() > 0){
            YcxRutterParentResult result = list.get(0);
            result.setSelecteds(selectedService.getSelecteds(recordId));
            return result;
        }
        return null;
    }

    /**
     * 更新建议
     * @param recordId 记录ID
     * @param recom 建议
     */
    @Override
    public void updateRecome(Integer recordId, String recom) {
        YcxRutterParentResult result = new YcxRutterParentResult();
        result.setRecome(recom);
        Example example = new Example(YcxRutterParentResult.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("recordId",recordId);
        ycxRutterParentResultMapper.updateByExampleSelective(result,example);

    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }
}