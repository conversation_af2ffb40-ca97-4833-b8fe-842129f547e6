<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.pdms.mapper.YcxPdmsSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.pdms.model.YcxPdmsSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="pdms_id" jdbcType="INTEGER" property="pdmsId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
    <select id="getSelected" resultMap="BaseResultMap">
      select sele.*,
      pdms.content,
      pdms.order_num,
      op.name as optionName
      from
        ycx_pdms_selected as sele
          inner join
        ycx_pdms as pdms
        ON
          pdms.id = sele.pdms_id
          left join
        ycx_pdms_option as op
        on
            op.pdms_id = pdms.id
            and
            op.value = sele.answer
      where
        sele.pdms_id = 1
      order by pdms.order_num asc
    </select>
</mapper>