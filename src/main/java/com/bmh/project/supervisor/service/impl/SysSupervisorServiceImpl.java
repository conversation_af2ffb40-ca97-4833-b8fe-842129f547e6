package com.bmh.project.supervisor.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.constant.Constants;
import com.bmh.common.security.SecurityUtil;
import com.bmh.common.security.model.LoginBody;
import com.bmh.common.security.model.LoginUser;
import com.bmh.common.security.service.LoginService;
import com.bmh.common.security.service.TokenService;
import com.bmh.project.supervisor.mapper.SysSupervisorMapper;
import com.bmh.project.supervisor.model.SysSupervisor;
import com.bmh.project.supervisor.service.SysSupervisorService;
import com.bmh.project.user.mapper.SysUserMapper;
import com.bmh.project.user.model.SysUser;
import org.springframework.context.annotation.Lazy;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SysSupervisorService对应的实现类
 *
 * <AUTHOR>
 * @date 2023年07月24日 11:21:32
 */
@Service
public class SysSupervisorServiceImpl extends BaseServiceImpl<SysSupervisor> implements SysSupervisorService {
    @Resource
    private SysSupervisorMapper sysSupervisorMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    @Lazy
    private LoginService loginService;
    @Resource
    private TokenService tokenService;

    /**
     * 默认密码
     */
    private final String DEFAULT_PSW = "qingsong123";

    /**
     * 查询登录督导
     *
     * @param loginBody 登录参数
     * @return
     */
    @Override
    public SysSupervisor getSupervisorByLoginBody (LoginBody loginBody) {
        String mobile = loginBody.getUsername ();
        Example example = new Example (SysSupervisor.class);
        example.createCriteria ().andEqualTo ("mobile", mobile);
        List<SysSupervisor> users = this.selectByExample (example);
        return CollectionUtil.isNotEmpty (users) ? users.get (0) : null;
    }

    /**
     * 获取督导人员的机构用户模拟身份
     *
     * @param orgId 需要模拟的机构ID
     * @return
     */
    @Override
    public Map<String, Object> getMockSysUser (Integer orgId) {
        LoginBody loginBody = new LoginBody ();
        loginBody.setUsername(SecurityUtil.getUsername () + "_" + orgId + "_mock");
        loginBody.setPassword(DEFAULT_PSW);
        LoginUser loginUser = null;
        try {
            loginUser = loginService.login (loginBody);
        } catch (Exception e) {
            if(e instanceof UsernameNotFoundException){
                // 新增用户
                SysUser sysUser = new SysUser ();
                sysUser.setType(2);
                sysUser.setUsername(loginBody.getUsername ());
                sysUser.setPassword(SecurityUtil.encryptPassword (loginBody.getPassword ()));
                sysUser.setName("督导-" + SecurityUtil.getNickName ());
                sysUser.setRole("督导");
                sysUser.setSex(1);
                sysUser.setOrgId(orgId);
                sysUser.setStatus(1);
                sysUser.setCreateTime(new Date());
                sysUserMapper.insert (sysUser);
                sysUser.setUserNo ("QSCZDD" + String.format ("%06d", sysUser.getId ()));
                sysUserMapper.updateByPrimaryKeySelective (sysUser);
                // TODO 模拟用户的身份要怎么处理，建立一个督导？
                loginUser = loginService.login (loginBody);
            }
        }

        String token = tokenService.createJwtToken (loginUser);
        Map<String, Object> resultMap = new HashMap<> ();
        resultMap.put ("mock_user_" + Constants.TOKEN, token);
        resultMap.put ("mock_user", loginUser.getUser ());
        return resultMap;
    }
}
