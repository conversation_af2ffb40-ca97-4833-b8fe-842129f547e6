<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.cars.mapper.YcxCarsMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.cars.model.YcxCars">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.cars.model.YcxCarsOption" >
      <result column="cars_id" jdbcType="INTEGER" property="carsId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="value" jdbcType="VARCHAR" property="value" />
    </collection>


  </resultMap>


<!--  List<YcxCabs> getList();-->
  <select id="getList" resultMap="BaseResultMap" >
    select cars.*
    , op.cars_id
    , op.name
    , op.value
    from ycx_cars as cars
    left join ycx_cars_option as op
    on op.cars_id = cars.id
    order by cars.order_num asc, op.value asc, op.id asc
  </select>


</mapper>