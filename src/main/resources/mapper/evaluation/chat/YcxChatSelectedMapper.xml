<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.chat.mapper.YcxChatSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.chat.model.YcxChatSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="chat_id" jdbcType="INTEGER" property="chatId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />


    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="optionName" jdbcType="VARCHAR" property="optionName" />

  </resultMap>

<!--  List<YcxChatSelected> selectSelectedByType(Integer type, Integer recordId);-->
  <select id="selectSelectedByType" parameterType="Integer" resultMap="BaseResultMap" >
    select sele.*
    , chat.content
    , chat.order_num
    , op.name as optionName
    from ycx_chat_selected as sele
    inner join ycx_chat as chat
    on chat.id = sele.chat_id
    left join ycx_chat_option as op
    on op.chat_id = chat.id
    and op.value = sele.answer
    where sele.record_id = #{recordId}
    and chat.type = #{type}
    order by chat.order_num asc
  </select>



</mapper>