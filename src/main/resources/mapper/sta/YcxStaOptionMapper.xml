<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.sta.mapper.YcxStaOptionMapper">

    <resultMap id="YcxStaOptionMap" type="com.bmh.project.evaluation.sta.model.YcxStaOption">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="staId" column="sta_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="value" column="value" jdbcType="VARCHAR"/>
        <result property="score" column="score" jdbcType="INTEGER"/>
    </resultMap>

</mapper>

