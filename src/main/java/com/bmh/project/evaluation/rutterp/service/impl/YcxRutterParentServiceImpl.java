package com.bmh.project.evaluation.rutterp.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.rutterp.mapper.YcxRutterParentMapper;
import com.bmh.project.evaluation.rutterp.model.YcxRutterParent;
import com.bmh.project.evaluation.rutterp.service.YcxRutterParentService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * YcxRutterParentService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年08月16日 18:13:51
 */
@Service
public class YcxRutterParentServiceImpl extends BaseServiceImpl<YcxRutterParent> implements YcxRutterParentService {
    @Resource
    private YcxRutterParentMapper ycxRutterParentMapper;

    /**
     * 查询所有问题(父母问卷)
     * @return
     */
    @Override
    public List<YcxRutterParent> getRutterP() {
        return ycxRutterParentMapper.getRutterP();
    }
}