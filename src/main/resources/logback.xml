<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- <include resource="org/springframework/boot/logging/logback/base.xml" /> -->
    <property name="BIZ_PATTERN"
              value="%d{HH:mm:ss.SSS} -%5p [%X{SessionId}] %-30.30logger{10} : %m%n"/>

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${BIZ_PATTERN}</pattern>
        </encoder>
    </appender>

    <logger name="com.bmh" level="DEBUG" additivity="false">
        <appender-ref ref="console"/>
    </logger>

    <root level="info">
        <appender-ref ref="console"/>
    </root>
</configuration>