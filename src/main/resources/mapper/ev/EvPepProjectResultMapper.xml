<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ev.mapper.EvPepProjectResultMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ev.model.EvPepProjectResult">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="result_id" jdbcType="INTEGER" property="resultId" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="option_name" jdbcType="VARCHAR" property="optionName" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

    <select id="getSelectResultListByResutlId" resultType="com.bmh.project.ev.vo.PepSelectResultVo">
        select r.option_name as optionName,r.score,r.project_id as projectId
        from ev_pep_project_result r
        where r.is_delete = 0
        and r.result_id = #{resultId}

    </select>

    <select id="getDomainScore" resultType="com.bmh.project.ev.vo.PepDomainScoreVo">
        select r.domain_id as domainId,sum(score) as rawScore,
               sum(case when score=2 then 2 else 0 end ) as passScore,
               sum(case when score=1 then 1 else 0 end ) as halfPassScore
        from ev_pep_project_result r
        where r.is_delete = 0
          and r.result_id = #{resultId}
        group by r.domain_id

    </select>

</mapper>
