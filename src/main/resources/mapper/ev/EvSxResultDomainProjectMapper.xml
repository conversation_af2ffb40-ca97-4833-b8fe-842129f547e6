<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ev.mapper.EvSxResultDomainProjectMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ev.model.EvSxResultDomainProject">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="result_domain_id" jdbcType="INTEGER" property="resultDomainId" />
    <result column="no" jdbcType="INTEGER" property="no" />
    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

    <select id="getSelectResultList" resultType="com.bmh.project.ev.vo.EvSelectResultVo">
        select rdp.no,ROUND(rdp.score) as score,rdp.domain_project_id as domainProjectId
        from ev_sx_result_domain_project rdp
                 inner join ev_sx_result_domain rd on rdp.result_domain_id = rd.id  and rdp.`status`= 1 and rdp.is_delete = 0  and rd.`status`= 1 and rd.is_delete = 0
                 inner join (SELECT max(id) as id , children_id from ev_result  where status =1 and is_delete = 0  and type = 2 group by children_id) r  on rd.result_id = r.id
        where r.children_id = #{childrenId}
    </select>
</mapper>
