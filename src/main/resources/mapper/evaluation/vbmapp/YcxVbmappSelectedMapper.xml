<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.vbmapp.mapper.YcxVbmappSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.vbmapp.model.YcxVbmappSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="subject_id" jdbcType="INTEGER" property="subjectId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="subject_name" jdbcType="VARCHAR" property="subjectName" />
    <result column="keyword" jdbcType="VARCHAR" property="keyword" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
  </resultMap>
</mapper>