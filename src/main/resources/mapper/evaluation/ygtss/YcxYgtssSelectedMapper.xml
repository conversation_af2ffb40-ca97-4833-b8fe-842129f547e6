<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.ygtss.mapper.YcxYgtssSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.ygtss.model.YcxYgtssSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="ygtss_id" jdbcType="INTEGER" property="ygtssId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="text" jdbcType="VARCHAR" property="text" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
    <select id="getSelected" resultMap="BaseResultMap" parameterType="integer">
      select
        sele.*,
        ygtss.content,
        ygtss.order_num,
        op.name as optionName
      from
        ycx_ygtss_selected as sele
          inner join
        ycx_ygtss as ygtss
        on
          ygtss.id = sele.ygtss_id
          left join
        ycx_ygtss_option as op
        on
            op.ygtss_id = ygtss.id
            and
            op.value = sele.answer
      where
        sele.record_id = #{recordId}
      order by ygtss.order_num asc
    </select>
</mapper>