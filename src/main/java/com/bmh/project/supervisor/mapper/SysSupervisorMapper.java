package com.bmh.project.supervisor.mapper;

import cn.hutool.core.date.DateTime;
import com.bmh.common.base.BaseMapper;
import com.bmh.project.dashboard.vo.SupervisorRankVo;
import com.bmh.project.supervisor.model.SysSupervisor;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Map;

public interface SysSupervisorMapper extends BaseMapper<SysSupervisor> {
    /**
     * 查询机构督导督导课程排行榜
     * @param orgId 机构id
     * @return 数据列表
     */
    List<SupervisorRankVo> getAbaSupervisorCourseCount(@Param("orgId") Integer orgId, @Param("startTime") DateTime startTime, @Param("endTime") DateTime endTime, @Param("removeSupervisorList") List<Integer> removeSupervisorList);

    List<SupervisorRankVo> getStSupervisorCourseCount(@Param("orgId") Integer orgId, @Param("startTime") DateTime startTime, @Param("endTime") DateTime endTime, @Param("removeSupervisorList") List<Integer> removeSupervisorList);

    List<SupervisorRankVo> getOrgAbaSupervisorCourseCount(@Param("startTime") DateTime startTime, @Param("endTime") DateTime endTime, @Param("removeSupervisorList") List<Integer> removeSupervisorList);

    List<SupervisorRankVo> getOrgStSupervisorCourseCount(@Param("startTime") DateTime startTime, @Param("endTime") DateTime endTime, @Param("removeSupervisorList") List<Integer> removeSupervisorList);

    /**
     * 查询机构下督导人数
     * @return
     */
    List<Map<String, Object>> selectOrgSupervisorCount();

}
