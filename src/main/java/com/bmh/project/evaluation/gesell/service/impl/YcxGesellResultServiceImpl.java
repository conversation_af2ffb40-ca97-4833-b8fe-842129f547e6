package com.bmh.project.evaluation.gesell.service.impl;

import cn.hutool.json.JSONUtil;
import com.bmh.common.base.BaseServiceImpl;
import com.bmh.common.security.SecurityUtil;
import com.bmh.project.book.model.YcxChildrenEvaluatingProject;
import com.bmh.project.common.service.YcxResultService;
import com.bmh.project.evaluation.gesell.mapper.YcxGesellResultMapper;
import com.bmh.project.evaluation.gesell.model.YcxGesellResult;
import com.bmh.project.evaluation.gesell.model.YcxGesellSelected;
import com.bmh.project.evaluation.gesell.service.YcxGesellResultService;
import com.bmh.project.evaluation.gesell.service.YcxGesellSelectedService;
import com.bmh.project.record.model.YcxChildrenRecord;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * YcxGesellResultService对应的实现类
 *
 * <AUTHOR>
 * @date 2021年06月15日 12:20:01
 */
@Service("4")
public class YcxGesellResultServiceImpl extends BaseServiceImpl<YcxGesellResult> implements YcxGesellResultService, YcxResultService<YcxGesellResult> {
    @Resource
    private YcxGesellResultMapper ycxGesellResultMapper;
    @Resource
    private YcxGesellSelectedService gesellSelectedService;

    @Override
    public void saveResult(YcxChildrenRecord childrenRecord) {
        YcxGesellResult gesellResult = JSONUtil.toBean (childrenRecord.getResultStr (), YcxGesellResult.class);
        gesellResult.setRecordId(childrenRecord.getId());
        gesellResult.setChildrenAge(childrenRecord.getChildrenAge());
        gesellResult.setChildrenId(childrenRecord.getChildrenId());
        gesellResult.setOrgId(childrenRecord.getOrgId());
        gesellResult.setOperatorId(SecurityUtil.getUserId());
        gesellResult.setOperatorName(SecurityUtil.getNickName());
        gesellResult.setCreateTime(new Date());
        this.insert(gesellResult);

        List<YcxGesellSelected> gesellSelecteds = gesellResult.getGesellSelecteds();
        gesellSelectedService.saveSelecteds(gesellSelecteds, gesellResult.getRecordId());

    }



    @Override
    public YcxGesellResult getResult(Integer recordId) {
        Example example = new Example(YcxGesellResult.class);
        example.createCriteria().andEqualTo("recordId", recordId);
        List<YcxGesellResult> list = this.selectByExample(example);
        if(list!=null&&list.size()>0){
            return list.get(0);
        }
        return null;
    }

    /**
     * 更新建议
     *
     * @param recordId 记录ID
     * @param recom    建议
     */
    @Override
    public void updateRecome (Integer recordId, String recom) {
        YcxGesellResult result = new YcxGesellResult ();
        result.setRecom (recom);
        Example example = new Example (YcxGesellResult.class);
        Example.Criteria criteria = example.createCriteria ();
        criteria.andEqualTo ("recordId", recordId);
        ycxGesellResultMapper.updateByExampleSelective (result,example);
    }

    @Override
    public void setAnswerForSubject(List list, Integer evaluatingId, Integer projectId) {

    }

    @Override
    public void computeResult(YcxChildrenEvaluatingProject record) {

    }


}