package com.bmh.project.evaluation.vbmapp.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_vbmapp_selected")
public class YcxVbmappSelected implements Serializable {
    /**
     * id
     */
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 单位ID
     */
    @Column(name = "org_id")
    private Integer orgId;

    /**
     * 题目Id
     */
    @Column(name = "subject_id")
    private Integer subjectId;

    /**
     * 儿童Id
     */
    @Column(name = "child_id")
    private Integer childId;

    /**
     * 项目名称
     */
    @Column(name = "subject_name")
    private String subjectName;

    /**
     * 答案
     */
    @Column(name = "keyword")
    private String keyword;

    /**
     * 状态(0无效，1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 删除时间
     */
    @Column(name = "delete_time")
    private Date deleteTime;

    /**
     * 操作人ID
     */
    @Column(name = "operator_id")
    private Integer operatorId;

    /**
     * 操作人姓名
     */
    @Column(name = "operator_name")
    private String operatorName;

    private static final long serialVersionUID = 1L;
}