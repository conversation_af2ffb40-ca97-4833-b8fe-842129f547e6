<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ev.mapper.EvChildrenMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ev.model.EvChildren">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
    <select id="selectNotEndChildren" resultType="com.bmh.project.ev.model.EvChildren">
        select
            yc.id,
            yc.name,
            yc.gender,
            yc.birthday,
            yc.logo,
            yc.link_mobile
        from ev_children ec
        left join ycx_children_config ycc on ec.id = ycc.id
        left join ycx_children yc on ec.id = yc.id
        where ycc.aba_status = 0
        and yc.org_id = #{orgId}
        <if test="name!=null and name!=''">
            and yc.name like concat('%', #{name}, '%')
        </if>
    </select>
</mapper>
