<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.prs.mapper.YcxPrsMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.prs.model.YcxPrs">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.prs.model.YcxPrsOption">
      <result column="prs_id" property="prsId"/>
      <result column="name" property="name"/>
      <result column="value" property="value"/>
      <result column="score" property="score"/>
    </collection>
  </resultMap>

  <select id="getPrsList" resultMap="BaseResultMap">
    select prs.*
    , op.prs_id
    , op.name
    , op.value
    , op.score
    from ycx_prs as prs
    inner join ycx_prs_option as op
    on op.prs_id = prs.id
    where prs.status = 1
    order by prs.order_num asc, op.id asc
  </select>
</mapper>