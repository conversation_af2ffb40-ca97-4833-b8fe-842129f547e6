<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.ddst.mapper.YcxDdstResultMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.ddst.model.YcxDdstResult">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="children_age" jdbcType="DECIMAL" property="childrenAge" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="res_type1" jdbcType="VARCHAR" property="resType1" />
    <result column="res_type2" jdbcType="VARCHAR" property="resType2" />
    <result column="res_type3" jdbcType="VARCHAR" property="resType3" />
    <result column="res_type4" jdbcType="VARCHAR" property="resType4" />
    <result column="recom" jdbcType="VARCHAR" property="recom" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="result_his" jdbcType="VARCHAR" property="resultHis" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>