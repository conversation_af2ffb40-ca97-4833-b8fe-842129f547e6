<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.user.mapper.SysParentMapper">

    <resultMap id="SysParentMap" type="com.bmh.project.user.model.SysParent">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="erpUserId" column="erp_user_id" jdbcType="INTEGER"/>
        <result property="orgId" column="org_id" jdbcType="INTEGER"/>
        <result property="name" column="name" jdbcType="VARCHAR"/>
        <result property="phone" column="phone" jdbcType="VARCHAR"/>
        <result property="gender" column="gender" jdbcType="INTEGER"/>
        <result property="address" column="address" jdbcType="VARCHAR"/>
        <result property="status" column="status" jdbcType="INTEGER"/>
        <result property="wxOpenId" column="wx_open_id" jdbcType="VARCHAR"/>
        <result property="wxUnionId" column="wx_union_id" jdbcType="VARCHAR"/>
        <result property="isDelete" column="is_delete" jdbcType="INTEGER"/>
        <result property="createName" column="create_name" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
        <result property="logo" column="logo" jdbcType="VARCHAR"/>
    </resultMap>
    <update id="updateListById">
        UPDATE sys_parent
        SET org_id = CASE id
        <foreach collection="list" item="item">
            WHEN #{item.id} THEN #{item.orgId}
        </foreach>
        ELSE org_id
        END
        WHERE id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item.id}
        </foreach>
    </update>
    <select id="getWashDate" resultMap="SysParentMap">
        SELECT
            *
        FROM
            sys_parent
        WHERE
            phone IN (
            SELECT
                phone
            FROM
                (SELECT phone, COUNT(id) a FROM sys_parent WHERE `status` = 1 AND is_delete = 0 AND phone NOT IN ('15613940777', '15373200091', '18331908987') GROUP BY phone HAVING a > 1) a)
        ORDER BY
            phone,
            create_time DESC
    </select>

</mapper>

