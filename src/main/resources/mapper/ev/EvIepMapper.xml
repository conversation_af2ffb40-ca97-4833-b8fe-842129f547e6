<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ev.mapper.EvIepMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ev.model.EvIep">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="result_id" jdbcType="INTEGER" property="resultId" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_level" jdbcType="INTEGER" property="projectLevel" />
    <result column="short_goal_id" jdbcType="INTEGER" property="shortGoalId" />
    <result column="short_goal_name" jdbcType="VARCHAR" property="shortGoalName" />
    <result column="assist" jdbcType="VARCHAR" property="assist" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_User" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_User" jdbcType="VARCHAR" property="updateUser" />
    <result column="target" jdbcType="LONGVARCHAR" property="target" />
  </resultMap>

  <update id="deleteByResultId">
    update ev_iep set is_delete = 1 where result_id = #{resultId}
  </update>
  <update id="updateIepPlanId">
    update ev_iep set plan_id = #{planId} where result_id = #{resultId}
  </update>


  <select id="selectGroupedResultIds" resultType="java.lang.Integer">
        SELECT result_id
        FROM ev_iep
        WHERE children_id = #{childrenId}
          AND org_id = #{orgId}
          AND is_delete = 0
        GROUP BY result_id
        ORDER BY MAX(status) ASC, MAX(create_time) DESC
            LIMIT #{offset}, #{pageSize}
    </select>
</mapper>