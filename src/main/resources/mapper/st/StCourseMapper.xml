<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.st.mapper.StCourseMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.st.model.StCourse">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="teacher_id" jdbcType="INTEGER" property="teacherId" />
    <result column="teacher_name" jdbcType="VARCHAR" property="teacherName" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="erp_task_id" jdbcType="INTEGER" property="erpTaskId"/>
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="getMonthDate" resultType="java.lang.String">
    select distinct DATE_FORMAT(start_time,'%Y-%m-%d') date
    from st_course
    where child_id = #{childId}
    and DATE_FORMAT(start_time, '%Y-%m') = #{month}
    and status = 2
    order by date desc
  </select>
    <select id="getReportCourseInfo" resultType="com.bmh.project.report.vo.StCourseVo">
        SELECT sc.teacher_name teacherName,
               sca.level_name       levelName,
               sca.domain_name      domainName,
               sca.project_name     projectName,
               sca.activity_name    activityName,
               scat.target_name     targetName,
               scr.support_type     supportType,
               scr.all_support_type allSupportType,
               sc.start_time        startTime,
               sc.end_time          endTime,
               scs.sign_url signUrl
        FROM st_course sc
                 LEFT JOIN st_course_activity sca ON sc.id = sca.course_id
                 LEFT JOIN st_course_activity_target scat ON sca.id = scat.course_activity_id
                 LEFT JOIN st_course_record scr ON scat.target_id = scr.course_target_id
                 LEFT JOIN sys_course_sign scs ON sc.id = scs.course_id
        WHERE sc.child_id = #{childrenId}
          AND sc.`status` = 2
          AND sc.end_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY sc.start_time
    </select>

    <insert id="autoSignCourse">
        INSERT INTO  `sys_course_sign` (`org_id`, `parent_id`, `child_id`, `course_type`, `course_id`, `teacher_id`, `teacher_name`, `start_time`, `end_time`,  `sign_status`, `status`, `create_time`, `create_user`)
        SELECT a.`org_id`, c.`parent_id`, a.`child_id`, 2 as `course_type`, a.id as `course_id`, a.`teacher_id`, a.`teacher_name`, a.`start_time`, NOW() as `end_time`,  0 as `sign_status`, 1 as `status`, NOW() as `create_time`, '系统自动结束' as `create_user`
        FROM st_course  a
                 INNER JOIN sys_org o on a.org_id = o.id and o.is_course_sign = 1
                 INNER JOIN ycx_children c on a.child_id = c.id
        WHERE a.status = 1
          and date(a.start_time)>=date(#{dateFrom})
    </insert>
</mapper>
