<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.behavior.mapper.YcxBehaviorMapper">

  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.behavior.model.YcxBehavior">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="month" jdbcType="INTEGER" property="month" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="tips" jdbcType="VARCHAR" property="tips" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="requirements" jdbcType="VARCHAR" property="requirements" />
    <result column="example_img" jdbcType="VARCHAR" property="exampleImg" />
    <result column="props" jdbcType="VARCHAR" property="props" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="is_blank" jdbcType="INTEGER" property="isBlank" />
    <result column="item_score" jdbcType="VARCHAR" property="itemScore" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>


<!--  List<YcxBehavior> getBehaviors(Integer type);-->
  <select id="getBehaviors" resultMap="BaseResultMap" useCache="true" >
    select beh.*
    from ycx_behavior as beh
    where beh.type = #{type}
  </select>


</mapper>