<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.user.mapper.SysAreaMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.user.model.SysArea">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="pid" jdbcType="INTEGER" property="pid" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>
</mapper>