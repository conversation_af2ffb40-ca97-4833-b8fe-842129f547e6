<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.nbna.mapper.YcxNbnaOptionMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.nbna.model.YcxNbnaOption">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="nbna_id" jdbcType="INTEGER" property="nbnaId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="value" jdbcType="VARCHAR" property="value" />
    <result column="score" jdbcType="INTEGER" property="score" />
  </resultMap>
</mapper>