<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.lmt.mapper.YcxLmtPrelResultMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.lmt.model.YcxLmtPrelResult">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="result" jdbcType="VARCHAR" property="result" />
    <result column="operator_type" jdbcType="INTEGER" property="operatorType" />
    <result column="operator_id" jdbcType="INTEGER" property="operatorId" />
    <result column="operator_name" jdbcType="VARCHAR" property="operatorName" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
    <select id="selectAnswerAndContent" resultType="com.bmh.project.report.vo.ReportLMTInfoVo">
      SELECT
      ylp.domain_name domainName,
      ylp.content,
      ylps.answer
      FROM
      ycx_lmt_prel_result ylpr
      LEFT JOIN ycx_lmt_prel_selected ylps ON ylpr.id = ylps.result_id
      LEFT JOIN ycx_lmt_prel ylp ON ylps.lmt_prel_id = ylp.id
      WHERE
      ylp.`status` = 1
      AND ylps.`status` = 1
      AND ylpr.children_id = #{childrenId}
      AND ylpr.record_id = #{recordId}
    </select>
</mapper>