<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.homework.mapper.AbaHwRecordMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.homework.model.AbaHwRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
      <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="hw_id" jdbcType="INTEGER" property="hwId" />
    <result column="hw_project_id" jdbcType="INTEGER" property="hwProjectId" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="record_date" jdbcType="DATE" property="recordDate" />
    <result column="record_type" jdbcType="INTEGER" property="recordType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />


    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
  </resultMap>

  <select id="getMonthDate" resultType="java.lang.String">
      select DATE_FORMAT(record_date,'%Y-%m-%d')
      from aba_hw_record
      where child_id = #{childId}
        and DATE_FORMAT(record_date, '%Y-%m') = #{month}
      group by record_date
      order by record_date desc
    </select>

  <select id="getList" resultMap="BaseResultMap">
      select ahr.*, ahp.project_name
      from aba_hw_record ahr
      left join aba_hw_project ahp on ahp.id = ahr.hw_project_id
      <where>
          <if test="hwId != null">
              and ahr.hw_id = #{hwId}
          </if>
          <if test="childId != null">
              and ahr.child_id = #{childId}
          </if>
          <if test="hwProjectId != null">
              and ahr.hw_project_id = #{hwProjectId}
          </if>
          <if test="recordDate != null">
              and ahr.record_date = #{recordDate}
          </if>
          <if test="projectId != null">
              and ahr.project_id = #{projectId}
          </if>
          <if test="recordType != null">
              and ahr.record_type = #{recordType}
          </if>
          <if test="status != null">
              and ahr.status = #{status}
          </if>
      </where>
      order by ahr.record_date desc, ahr.create_time
  </select>
</mapper>