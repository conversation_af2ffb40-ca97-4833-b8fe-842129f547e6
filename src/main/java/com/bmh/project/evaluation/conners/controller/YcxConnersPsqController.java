package com.bmh.project.evaluation.conners.controller;

import com.bmh.common.base.Result;
import com.bmh.common.base.ResultUtil;
import com.bmh.project.evaluation.conners.model.YcxConnersPsq;
import com.bmh.project.evaluation.conners.service.YcxConnersPsqService;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * Conners儿童行为问卷父母用量表PSQ
 *
 * <AUTHOR>
 * @since 2021/7/10 15:03
 */
@RestController
@RequestMapping("/conners/psq")
public class YcxConnersPsqController {

    @Resource
    private YcxConnersPsqService psqService;

    /**
     * 根据类型获取问题
     * @param type 类型(1品行问题，2学习问题，3心身问题，4冲动-多动， 5焦虑，6其他)
     * @return
     */
    @RequestMapping("/getPsqListByType")
    public Result<List<YcxConnersPsq>> getPsqListByType(Integer type){
        List<YcxConnersPsq> list = psqService.getListByType (type);
        return ResultUtil.success (list);
    }
}
