<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ev.mapper.EvResultVbmappMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ev.model.EvResultVbmapp">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="result_id" jdbcType="INTEGER" property="resultId" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="milestone_summary" jdbcType="LONGVARCHAR" property="milestoneSummary" />
    <result column="obstacle_summary" jdbcType="LONGVARCHAR" property="obstacleSummary" />
    <result column="transfer_summary" jdbcType="LONGVARCHAR" property="transferSummary" />
    <result column="milestone_report" jdbcType="LONGVARCHAR" property="milestoneReport" />
    <result column="obstacle_report" jdbcType="LONGVARCHAR" property="obstacleReport" />
    <result column="transfer_report" jdbcType="LONGVARCHAR" property="transferReport" />
    <result column="assess_question" jdbcType="LONGVARCHAR" property="assessQuestion" />
    <result column="assess_result" jdbcType="LONGVARCHAR" property="assessResult" />
    <result column="advance" jdbcType="LONGVARCHAR" property="advance" />
    <result column="referral_suggestion" jdbcType="LONGVARCHAR" property="referralSuggestion" />
  </resultMap>

    <select id="getResultVbByResutlId" resultMap="BaseResultMap">
        select id, assess_question, assess_result, advance, referral_suggestion
        from ev_result_vbmapp
        where result_id = #{resultId}
        limit 1
    </select>

    <select id="selectByChildrenId" resultType="com.bmh.project.ev.vo.ResultPageListVo">
        SELECT
            case when r.type = 0 then vb.id else r.id end as id ,
            r.type,
            r.status,
            r.create_user as createUser,
            r.create_time  as createTime
        from ev_result  r
                 left JOIN ev_result_vbmapp vb  on vb.result_id = r.id and vb.is_delete = 0
        where r.status = 1
          and r.type in (0,1)
          and r.children_id = #{childrenId}
        ORDER BY r.create_time desc
    </select>
    <select id="selectHistoryList" resultType="com.bmh.project.report.vo.ReportLMTTitleVo">
        select
            id as recordId, "VBMAPP" as shortTitle, "VBMAPP评估" as title, create_time as createdTime
        from ev_result_vbmapp
        where children_id = #{id}
        and status = 1
        and is_delete = 0
        order by create_time
    </select>

    <select id="getVbTransferScore" resultType="com.bmh.project.ev.vo.VbScoreTotalVo">
        SELECT  sum(case when d.vb_type = 1 then p.score else 0 end ) as milestoneTotalScore,
                sum(case when d.vb_type = 2 then p.score else 0 end ) as obstacleTotalScore,
                sum(case when d.vb_type = 2 and p.domain_project_id in (380,381) then p.score else 0 end ) as obstacleNegativeScore,
                sum(case when d.vb_type = 1 and d.id = 19  then p.score else 0 end ) as milestoneClassScore,
                sum(case when d.vb_type = 1 and d.id = 12 then p.score else 0 end ) as milestoneGameScore
        from ev_vb_result_domain  rd
                 INNER JOIN ev_vb_result_domain_project p on rd.id = p.result_domain_id and rd.status=1 and rd.is_delete=0 and p.status=1 and p.is_delete=0
                 INNER JOIN ev_domain d on  rd.domain_id = d.id and  d.type = 0 and d.status=1 and d.is_delete=0
        where d.type = 0
          and rd.result_id = #{resultId}
    </select>

    <update id="updateTransferReport">
       update ev_result_vbmapp set transfer_report = #{transferReport},update_user = #{updateUser},update_time = #{updateTime}
       where id = #{id}
    </update>



</mapper>
