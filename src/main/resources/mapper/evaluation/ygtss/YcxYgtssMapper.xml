<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.ygtss.mapper.YcxYgtssMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.ygtss.model.YcxYgtss">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="grade" jdbcType="INTEGER" property="grade" />
    <result column="parentId" jdbcType="INTEGER" property="parentid" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="created_time" jdbcType="TIMESTAMP" property="createdTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.ygtss.model.YcxYgtssOption" >
      <result column="ygtss_id" jdbcType="INTEGER" property="ygtssId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="opType" jdbcType="INTEGER" property="type" />
      <result column="value" jdbcType="VARCHAR" property="value" />
      <result column="score" jdbcType="VARCHAR" property="score" />
    </collection>

  </resultMap>
    <select id="getYgtssByType" resultMap="BaseResultMap">
      select ys.*
        , op.ygtss_id
        , op.name
        , op.value
        , op.score
        , op.type as opType
    from ycx_ygtss ys
	left join ycx_ygtss_option as op
	on op.ygtss_id = ys.id
	where ys.type = #{type}
	and ys.status = 1
	and ys.grade = #{grade}
        <if test="parentId != null">
            and ys.parentId = #{parentId}
        </if>
	order by
	ys.order_num asc,
	ys.id asc
    </select>
</mapper>