package com.bmh.project.evaluation.conners.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.conners.model.YcxConnersPsq;

import java.util.List;

/**
 * ycx_conners_psq表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年07月10日 14:43:46
 */
public interface YcxConnersPsqService extends BaseService<YcxConnersPsq> {

    /**
     * 根据类型获取问题
     * @param type 类型(1品行问题，2学习问题，3心身问题，4冲动-多动， 5焦虑，6其他)
     * @return
     */
    List<YcxConnersPsq> getListByType(Integer type);
}