<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.lmt.mapper.YcxLmtPrelMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.lmt.model.YcxLmtPrel">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />

    <result column="checkOptionValue" jdbcType="VARCHAR" property="checkOptionValue" />
    <collection property="options" ofType="com.bmh.project.evaluation.lmt.model.YcxLmtPrelOption">
      <result column="lmt_prel_id" jdbcType="INTEGER" property="lmtPrelId" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="value" jdbcType="VARCHAR" property="value" />
      <result column="score" jdbcType="INTEGER" property="score" />
    </collection>

  </resultMap>


  <!--获取问题列表-->
  <select id="getList" resultMap="BaseResultMap">
      select prel.*,
             op.lmt_prel_id,
             op.name,
             op.value,
             op.score
      <if test="resultId != null">
          , ylps.answer as checkOptionValue
      </if>
      from ycx_lmt_prel as prel
          left join ycx_lmt_prel_option as op on op.lmt_prel_id = prel.id
      <if test="resultId != null">
          left join ycx_lmt_prel_selected ylps on ylps.lmt_prel_id = prel.id and ylps.result_id = #{resultId}
            and ylps.status = 1
      </if>
      where prel.status = 1
      <if test="domainId != null ">
          and prel.domain_id = #{domainId}
      </if>
      order by prel.domain_id asc, prel.order_num asc, op.id asc
  </select>
</mapper>