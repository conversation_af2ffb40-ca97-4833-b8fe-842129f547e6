<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.st.mapper.StActivityTargetMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.st.model.StActivityTarget">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="activity_id" jdbcType="INTEGER" property="activityId" />
    <result column="activity_name" jdbcType="VARCHAR" property="activityName" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="suggest_duration" jdbcType="VARCHAR" property="suggestDuration" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
    <select id="getTargetListByActivityId" resultType="com.bmh.project.st.model.StActivityTarget">
        select
            id,
            name,
            activity_id AS activityId,
            4 AS status
        from st_activity_target
        where activity_id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>
