<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.nyls.mapper.YcxNylsDictMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.nyls.model.YcxNylsDict">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="nyls_type" jdbcType="INTEGER" property="nylsType" />
    <result column="sex" jdbcType="INTEGER" property="sex" />
    <result column="age" jdbcType="INTEGER" property="age" />
    <result column="avg" jdbcType="INTEGER" property="avg" />
    <result column="differ" jdbcType="INTEGER" property="differ" />
  </resultMap>
    <select id="getNylsDict" resultMap="BaseResultMap">
      select d.*
        from ycx_nyls_dict as d
        where d.sex = #{sex}
        and d.age = #{age}
    </select>
</mapper>