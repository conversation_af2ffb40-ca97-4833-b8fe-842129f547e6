<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaPrepStatisticsMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaPrepStatistics">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="org_id" jdbcType="INTEGER" property="orgId"/>
        <result column="teacher_id" jdbcType="INTEGER" property="teacherId"/>
        <result column="teacher_name" jdbcType="VARCHAR" property="teacherName"/>
        <result column="prep_date" jdbcType="DATE" property="prepDate"/>
        <result column="prep_ids" jdbcType="VARCHAR" property="prepIds"/>
        <result column="prep_rate" jdbcType="DOUBLE" property="prepRate"/>
        <result column="course_ids" jdbcType="VARCHAR" property="courseIds"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    </resultMap>

    <select id="getTeacherList" resultType="com.bmh.project.aba.vo.AbaPrepStatisticsTeacherListVo">
        SELECT s.org_id                                                    AS orgId,
               s.teacher_id                                                AS teacherId,
               s.teacher_name                                              AS teacherName,
               s.prep_rate                                                 AS recent1PrepRate,-- 最近一次备课率
               ROUND(AVG(CASE WHEN s.rn &lt;= 7 THEN s.prep_rate END), 2)  AS recent7PrepRate,-- 最近7次备课率平均值
               ROUND(AVG(CASE WHEN s.rn &lt;= 30 THEN s.prep_rate END), 2) AS recent30PrepRate -- 最近30次备课率平均值
        FROM
        (
        SELECT org_id,
               teacher_id,
               teacher_name,
               prep_rate,
               @row_number := IF(@current_teacher = teacher_id, @row_number + 1, 1) AS rn,
               @current_teacher := teacher_id -- 分组字段
        FROM aba_prep_statistics,
             (SELECT @row_number := 0, @current_teacher := '') AS vars
        <where>
            <if test="orgId != null">
                AND org_id = #{orgId}
            </if>
            <if test="teacherName != null and teacherName != ''">
                AND teacher_name LIKE CONCAT('%', #{teacherName}, '%')
            </if>
        </where>
        ORDER BY teacher_id,
                 prep_date DESC
        ) s
        GROUP BY s.teacher_id
        ORDER BY recent1PrepRate DESC, recent7PrepRate DESC, recent30PrepRate DESC
    </select>

    <select id="getBaseDataList" resultType="com.bmh.project.aba.dto.AbaPrepStatisticsBaseDto">
        SELECT ac.org_id       AS orgId,
               ac.child_id     AS childId,
               ac.teacher_id   AS teacherId,
               ac.teacher_name AS teacherName,
               ac.plan_id      AS planId,
--                ap.id           AS prepId,
--                ap.prep_date    AS prepDate,
--                ap.prep_rate    AS prepRate,
               ac.id           AS courseId
        FROM aba_course ac
--                  LEFT JOIN aba_prep ap ON ac.org_id = ap.org_id
--             AND ac.child_id = ap.child_id
--             AND ac.teacher_id = ap.teacher_id
--             AND DATE(ac.end_time) = ap.prep_date
        WHERE ac.`status` = 2
          AND DATE(ac.end_time) = #{dateStr}
    </select>
</mapper>
