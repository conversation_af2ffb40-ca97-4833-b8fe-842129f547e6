<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.mcc.mapper.MccCourseMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.mcc.model.MccCourse">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="org_id" jdbcType="INTEGER" property="orgId"/>
        <result column="org_lesson_plan_id" jdbcType="INTEGER" property="orgLessonPlanId"/>
        <result column="teacher_id" jdbcType="INTEGER" property="teacherId"/>
        <result column="teacher_name" jdbcType="VARCHAR" property="teacherName"/>
        <result column="lesson_plan_id" jdbcType="INTEGER" property="lessonPlanId"/>
        <result column="lesson_plan_name" jdbcType="VARCHAR" property="lessonPlanName"/>
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    </resultMap>

    <select id="getMonthDate" resultType="java.lang.String">
        select distinct DATE_FORMAT(mc.start_time, '%Y-%m-%d')
        from mcc_course mc
        <if test="childId != null">
            left join mcc_course_person mcp on mcp.type = 3 and mcp.course_id = mc.id
        </if>
        where mc.status = 2
          and DATE_FORMAT(mc.start_time, '%Y-%m') = #{month}
        <if test="orgLessonPlanId != null">
            and mc.org_lesson_plan_id = #{orgLessonPlanId}
        </if>
        <if test="childId != null">
            and mcp.person_id = #{childId}
        </if>
        order by DATE_FORMAT(mc.start_time, '%Y-%m-%d') desc
    </select>

    <select id="getCourseByChildId" resultMap="BaseResultMap">
        select mc.*
        from mcc_course mc
                 left join mcc_course_person mcp on mcp.type = 3 and mcp.course_id = mc.id
        where mc.status = 2
          and mcp.type = 3
          and mcp.person_id = #{childId}
          and date(mc.start_time) = date(#{date})
    </select>

    <select id="getCourseBaseInfoList" resultType="com.bmh.project.mcc.vo.MccCourseBaseInfoVo">
        select mc.id,
               mc.teacher_id                 as teacherId,
               mc.teacher_name               as teacherName,
               group_concat(mcp.person_name) as assistTeacherName,
               mc.org_lesson_plan_id         as orgLessonPlanId,
               mc.lesson_plan_id             as lessonPlanId,
               mc.lesson_plan_name           as lessonPlanName,
               mc.start_time                 as startTime,
               mc.end_time                   as endTime
        from mcc_course mc
                 left join mcc_course_person mcp on mcp.type = 2 and mc.id = mcp.course_id
        <where>
            <if test="id != null">
                and mc.id = #{id}
            </if>
            <if test="orgId">
                and mc.org_id = #{orgId}
            </if>
            <if test="orgLessonPlanId != null">
                and mc.org_lesson_plan_id = #{orgLessonPlanId}
            </if>
            <if test="teacherId != null">
                and mc.teacher_id = #{teacherId}
            </if>
            <if test="lessonPlanId != null">
                and mc.lesson_plan_id = #{lessonPlanId}
            </if>
            <if test="status != null">
                and mc.status = #{status}
            </if>
            <if test="courseDate != null">
                and date(mc.start_time) = date(#{courseDate})
            </if>
        </where>
        group by mc.id
        order by mc.start_time desc
    </select>
</mapper>