<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaRealiaMapper">

    <resultMap id="AbaRealiaMap" type="com.bmh.project.aba.model.AbaRealia">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="toolsName" column="tools_name" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="INTEGER"/>
        <result property="url" column="url" jdbcType="VARCHAR"/>
        <result property="level" column="level" jdbcType="INTEGER"/>
        <result property="realiaType" column="realia_type" jdbcType="INTEGER"/>
        <result property="remark" column="remark" jdbcType="VARCHAR"/>
        <result property="createUser" column="create_user" jdbcType="VARCHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateUser" column="update_user" jdbcType="VARCHAR"/>
    </resultMap>

    <select id="getRealiaListByProjectId" resultMap="AbaRealiaMap">
        SELECT r.*
        FROM aba_realia r
        WHERE r.level = 3
          AND r.url IS NOT NULL
          AND r.url != ''
          AND (r.parent_id IN (
            SELECT pr.realia_id FROM aba_project_realia pr WHERE pr.project_id = #{projectId}
            UNION
            SELECT r2.id FROM aba_realia r2 JOIN aba_project_realia pr ON r2.parent_id = pr.realia_id
            WHERE pr.project_id = #{projectId}
        )
            OR r.id IN (
                SELECT pr.realia_id FROM aba_project_realia pr WHERE pr.project_id = #{projectId}
                UNION
                SELECT r2.id FROM aba_realia r2 JOIN aba_project_realia pr ON r2.parent_id = pr.realia_id
                WHERE pr.project_id = #{projectId}
            ))
        LIMIT 3;
    </select>

</mapper>

