package com.bmh.project.evaluation.vbmapp.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.vbmapp.model.YcxVbmappRealm;
import com.bmh.project.evaluation.vbmapp.mapper.YcxVbmappRealmMapper;
import com.bmh.project.evaluation.vbmapp.service.YcxVbmappRealmService;
import javax.annotation.Resource;

import org.springframework.cache.annotation.Caching;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.stream.Collectors;

/**
 * YcxVbmappRealmService对应的实现类
 *
 * <AUTHOR>
 * @date 2022年11月21日 20:20:23
 */
@Service
public class YcxVbmappRealmServiceImpl extends BaseServiceImpl<YcxVbmappRealm> implements YcxVbmappRealmService {
    @Resource
    private YcxVbmappRealmMapper ycxVbmappRealmMapper;


    @Override
    public List<YcxVbmappRealm> getRealmList() {
        List<YcxVbmappRealm> allList = this.selectAll();
        List<YcxVbmappRealm> list = allList.stream()
                .filter(p -> p.getParentId() == 0)
                .collect(Collectors.toList());
        list.forEach(c->{
            List<YcxVbmappRealm> children = allList.stream()
                    .filter(p -> p.getParentId().equals(c.getId()))
                    .collect(Collectors.toList());
            c.setChildren(children);
        });
        return list;
    }

    @Override
    public List<YcxVbmappRealm> getQuickRealmList() {
        Example example = new Example(YcxVbmappRealm.class);
        example.createCriteria().andEqualTo("isQuick",1);
        List<YcxVbmappRealm> allList = this.selectByExample(example);
        List<YcxVbmappRealm> list = allList.stream()
                .filter(p -> p.getParentId() == 0)
                .collect(Collectors.toList());
        list.forEach(c->{
            List<YcxVbmappRealm> children = allList.stream()
                    .filter(p -> p.getParentId().equals(c.getId()))
                    .collect(Collectors.toList());
            c.setChildren(children);
        });
        return list;
    }
}