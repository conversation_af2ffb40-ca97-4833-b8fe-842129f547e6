<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.lmt.mapper.YcxLmtProjectMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.lmt.model.YcxLmtProject">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_name" jdbcType="VARCHAR" property="projectName" />
    <result column="project_level" jdbcType="INTEGER" property="projectLevel" />
    <result column="short_goal_id" jdbcType="INTEGER" property="shortGoalId" />
    <result column="short_goal_name" jdbcType="VARCHAR" property="shortGoalName" />
    <result column="pass_count" jdbcType="INTEGER" property="passCount" />
    <result column="fail_count" jdbcType="INTEGER" property="failCount" />
    <result column="pass_rate" jdbcType="DOUBLE" property="passRate" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="is_default" jdbcType="INTEGER" property="isDefault" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <resultMap id="YcxLmtProjectVoResultMap" type="com.bmh.project.evaluation.lmt.vo.YcxLmtProjectVo">
    <id column="id" property="id"/>
    <result column="domain_id" property="domainId"/>
    <result column="domain_name" property="domainName"/>
    <result column="project_id" property="projectId"/>
    <result column="project_name" property="projectName"/>
    <result column="project_level" property="projectLevel"/>
    <result column="short_goal_id" property="shortGoalId"/>
    <result column="short_goal_name" property="shortGoalName"/>
    <result column="pass_count" property="passCount"/>
    <result column="fail_count" property="failCount"/>
    <result column="pass_rate" property="passRate"/>
    <result column="is_default" property="isDefault" />
    <collection property="recordList" ofType="com.bmh.project.evaluation.lmt.vo.YcxLmtProjectRecordVo">
      <id column="record_id" property="id"/>
      <result column="lmt_project_id" property="lmtProjectId"/>
      <result column="target_done" property="targetDone"/>
    </collection>
  </resultMap>
  <select id="getList" resultMap="YcxLmtProjectVoResultMap">
      select ylp.id
           , ylp.domain_id
           , ylp.domain_name
           , ylp.project_id
           , ylp.project_name
           , ylp.project_level
           , ylp.short_goal_id
           , ylp.short_goal_name
           , ylp.pass_count
           , ylp.fail_count
           , ylp.pass_rate
           , ylp.is_default
           , ylpr.id as record_id
           , ylpr.lmt_project_id
           , ylpr.target_done
      from ycx_lmt_project ylp
               left join ycx_lmt_project_record ylpr
                         on ylp.record_id = ylpr.record_id and ylp.id = ylpr.lmt_project_id and ylpr.status = 1
      where ylp.record_id = #{recordId}
        and ylp.status = 1
      order by ylp.domain_id, ylp.id
  </select>
</mapper>