<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.csi.mapper.YcxCsiMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.csi.model.YcxCsi">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.csi.model.YcxCsiOption">
      <result column="csi_id" property="csiId"/>
      <result column="name" property="name"/>
      <result column="value" property="value"/>
      <result column="score" property="score"/>
    </collection>
  </resultMap>

  <select id="getCsiListByType" resultMap="BaseResultMap">
    select csi.*
    , op.csi_id
    , op.name
    , op.value
    , op.score
    from ycx_csi as csi
    inner join ycx_csi_option as op
    on op.csi_id = csi.id
    where csi.type = #{type}
    and csi.status = 1
    order by csi.order_num asc, op.id asc
  </select>
</mapper>