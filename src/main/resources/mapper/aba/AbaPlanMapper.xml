<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaPlanMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaPlan">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="project_min_round" jdbcType="INTEGER" property="projectMinRound" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
    <update id="updateByIdList" parameterType="java.util.List">
      <foreach collection="list" item="item" separator=";">
        update aba_plan set status = #{item.status} where id = #{item.id}
      </foreach>
    </update>
    <select id="getLastPlanId" resultType="java.lang.Integer">
        select id from aba_plan where child_id = #{childId} order by create_time desc limit 1
    </select>
    <select id="selectEndSupervisor" resultType="java.lang.Integer">
        SELECT
            ap.child_id
        FROM
            aba_plan ap
                INNER JOIN (SELECT child_id, MAX(create_time) AS create_time
                            FROM aba_plan
                            WHERE child_id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
                            GROUP BY child_id) a
                    ON ap.child_id = a.child_id
                AND ap.create_time = a.create_time
    </select>
</mapper>
