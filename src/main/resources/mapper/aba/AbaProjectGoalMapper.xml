<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaProjectGoalMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaProjectGoal">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="short_goal" jdbcType="VARCHAR" property="shortGoal" />
    <result column="operate_method" jdbcType="VARCHAR" property="operateMethod" />
    <result column="operate_video" jdbcType="VARCHAR" property="operateVideo" />
    <result column="notice" jdbcType="VARCHAR" property="notice" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
    <result column="assist_method" jdbcType="VARCHAR" property="assistMethod" />
  </resultMap>
    <select id="getGoalListByProjectId" resultType="com.bmh.project.aba.model.AbaProjectGoal">
        select
            id,
            short_goal AS shortGoal,
            project_id AS projectId,
            4 AS status
        from aba_project_goal
        where project_id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        order by id
    </select>
</mapper>
