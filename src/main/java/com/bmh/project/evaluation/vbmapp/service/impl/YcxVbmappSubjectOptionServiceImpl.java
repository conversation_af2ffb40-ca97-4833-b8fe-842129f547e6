package com.bmh.project.evaluation.vbmapp.service.impl;

import com.bmh.common.base.BaseServiceImpl;
import com.bmh.project.evaluation.vbmapp.model.YcxVbmappSubjectOption;
import com.bmh.project.evaluation.vbmapp.mapper.YcxVbmappSubjectOptionMapper;
import com.bmh.project.evaluation.vbmapp.service.YcxVbmappSubjectOptionService;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.stream.Collectors;

/**
 * YcxVbmappSubjectOptionService对应的实现类
 *
 * <AUTHOR>
 * @date 2022年11月21日 20:20:23
 */
@Service
public class YcxVbmappSubjectOptionServiceImpl extends BaseServiceImpl<YcxVbmappSubjectOption> implements YcxVbmappSubjectOptionService {
    @Resource
    private YcxVbmappSubjectOptionMapper ycxVbmappSubjectOptionMapper;

    @Override
    public List<YcxVbmappSubjectOption> getSubjectOption(Integer subjectId) {
        Example example=new Example(YcxVbmappSubjectOption.class);
        example.createCriteria().andEqualTo("subjectId",subjectId);
        example.orderBy("orderNum").asc();
        List<YcxVbmappSubjectOption> allList = this.selectByExample(example);

        List<YcxVbmappSubjectOption> list = allList.stream()
                .filter(p->p.getParentId()==0).collect(Collectors.toList());
        list.forEach(c->{
            c.setChildren(allList.stream()
                    .filter(p->p.getParentId().equals(c.getId()))
                    .collect(Collectors.toList()));
        });
        return list;
    }
}