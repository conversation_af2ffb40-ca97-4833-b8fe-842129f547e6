<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.ecaa.mapper.YcxEcaaMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.ecaa.model.YcxEcaa">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="type" jdbcType="INTEGER" property="type"/>
        <result column="tag" jdbcType="VARCHAR" property="tag"/>
        <result column="content" jdbcType="VARCHAR" property="content"/>
        <result column="order_num" jdbcType="INTEGER" property="orderNum"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="checkOptionValue" jdbcType="VARCHAR" property="checkOptionValue"/>

        <collection property="options" ofType="com.bmh.project.evaluation.ecaa.model.YcxEcaaOption">
            <result column="ecaa_id" jdbcType="INTEGER" property="ecaaId"/>
            <result column="name" jdbcType="VARCHAR" property="name"/>
            <result column="value" jdbcType="VARCHAR" property="value"/>
            <result column="score" jdbcType="VARCHAR" property="score"/>
        </collection>
    </resultMap>

    <select id="getEcaaByType" resultMap="BaseResultMap">
        select ecaa.*,
               op.ecaa_id,
               op.name,
               op.value,
               op.score
        <if test="recordId != null">
            ,selected.answer as checkOptionValue
        </if>
        from ycx_ecaa as ecaa
            left join ycx_ecaa_option as op on op.ecaa_id = ecaa.id
        <if test="recordId != null">
            left join ycx_ecaa_selected selected on selected.ecaa_id = ecaa.id and selected.record_id = #{recordId}
                and selected.status = 1
        </if>
        where ecaa.status = 1
        <if test="type != null">
            and ecaa.type = #{type}
        </if>
        order by ecaa.type, ecaa.order_num, op.id
    </select>
</mapper>