<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.user.mapper.SysUserMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.user.model.SysUser">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="erp_user_id" jdbcType="INTEGER" property="erpUserId" />
    <result column="user_no" jdbcType="VARCHAR" property="userNo" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="role" jdbcType="VARCHAR" property="role" />
    <result column="sex" jdbcType="INTEGER" property="sex" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="qrcode_url" jdbcType="VARCHAR" property="qrcodeUrl" />
    <result column="show_dabai" jdbcType="INTEGER" property="showDabai" />

  </resultMap>

  <select id="checkUserNameUnique" parameterType="String" resultType="int">
    select count(1) from sys_user where username = #{userName} limit 1
  </select>

  <select id="getAssistTeacherList" resultType="com.bmh.project.user.vo.SysUserDictVo">
      select su.id,
             su.user_no,
             su.type,
             su.username,
             su.name,
             su.logo,
             su.qrcode_url as qrcodeUrl,
             su.show_dabai as showDabai
      from sys_user su
      left join mcc_org_lesson_plan mopl on su.org_id = mopl.org_id and mopl.teacher_id = su.id and mopl.is_delete = 0
      where su.org_id = #{orgId}
      and mopl.id is null
  </select>
    <select id="selectByOrgId" resultType="java.lang.Integer">
        SELECT COUNT(id) FROM sys_user WHERE `status` = 1 AND org_id = #{orgId}
    </select>
</mapper>
