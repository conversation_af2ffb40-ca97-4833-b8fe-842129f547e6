<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.st.mapper.StCourseActivityTargetMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.st.model.StCourseActivityTarget">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="course_id" jdbcType="INTEGER" property="courseId"/>
        <result column="course_activity_id" jdbcType="INTEGER" property="courseActivityId"/>
        <result column="plan_activity_target_id" jdbcType="INTEGER" property="planActivityTargetId"/>
        <result column="target_id" jdbcType="INTEGER" property="targetId"/>
        <result column="target_name" jdbcType="VARCHAR" property="targetName"/>
        <result column="is_done" jdbcType="INTEGER" property="isDone"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    </resultMap>

    <insert id="insertBatchByCourse">
        insert into st_course_activity_target (course_id, course_activity_id, plan_activity_target_id, target_id,
                                               target_name, create_user,
                                               create_time, update_time, update_user)
        select #{courseId} as course_id,
               spa.id      as course_activity_id,
               spat.id     as plan_activity_target_id,
               spat.target_id,
               spat.target_name,
               spa.create_user,
               spa.create_time,
               spa.update_time,
               spa.update_user
        from st_course_activity spa
                 left join st_plan_activity_target spat on spa.plan_activity_id = spat.plan_activity_id and spat.`status` = 1
        where spa.course_id = #{courseId}
    </insert>

    <select id="getCourseTargetList" resultType="com.bmh.project.st.vo.StCourseActivityTargetVo">
        select scat.id                 as courseActivityTargetId,
               scat.course_activity_id as courseActivityId,
               sca.project_id          as projectId,
               sca.project_name        as projectName,
               sca.activity_id         as activityId,
               sca.activity_name       as activityName,
               sa.operate_video        as operateVideo,
               scat.target_id          as targetId,
               scat.target_name        as targetName,
               sat.suggest_duration    as suggestDuration,
               scat.is_done            as isDone,
               scr.id                  as courseRecordId,
               scr.support_type        as supportType,
               scr.all_support_type    as allSupportType
        from st_course_activity_target scat
                 left join st_activity_target sat on scat.target_id = sat.id
                 left join st_activity sa on sat.activity_id = sa.id
                 left join st_course_activity sca on scat.course_activity_id = sca.id
                 left join st_course_record scr on scat.id = scr.course_target_id
        where scat.course_id = #{courseId}
          and sca.domain_id = #{domainId}
        order by sca.project_id, sca.activity_id, scat.target_id
    </select>
    <select id="getTargetCount" resultType="java.lang.Integer">
        SELECT COUNT(scat.target_id)
        FROM st_course_activity_target scat
        LEFT JOIN st_course sc ON scat.course_id = sc.id
        WHERE sc.`status` = 2
          AND scat.create_time &gt; #{startTime}
          AND scat.create_time &lt; #{endTime}
          <if test="orgId!=0">
            AND sc.org_id = #{orgId}
          </if>
        <if test="0==orgId">
            AND sc.org_id NOT IN (1,19,43,65)
        </if>
    </select>
    <select id="getChildrenCompleteCount" resultType="com.bmh.project.dashboard.vo.ChildRankVo">
        SELECT yc.`name` AS childName,
               sca.child_id AS childId,
               COUNT(*)  AS shortGoalCount
        FROM st_plan_activity_target scat
                 LEFT JOIN st_plan_activity sca ON scat.plan_activity_id = sca.id
                 LEFT JOIN ycx_children yc ON sca.child_id = yc.id
        WHERE scat.update_time &gt; #{startTime}
          AND scat.update_time &lt; #{endTime}
          AND scat.status = 2
          AND sca.org_id = #{orgId}
        GROUP BY sca.child_id
    </select>
    <select id="selectTargetCount" resultType="java.lang.Integer">
        SELECT
        COUNT(DISTINCT (spat.target_id))
        FROM st_plan_activity_target spat
        LEFT JOIN st_plan sp ON spat.plan_id = sp.id
        <where>
            <if test="orgId!=0">
                AND sp.org_id = #{orgId}
            </if>
            <if test="0==orgId">
                AND sp.org_id NOT IN (1,19,43,65)
            </if>
        </where>
    </select>
    <select id="getOrgChildrenCompleteCount" resultType="com.bmh.project.dashboard.vo.ChildRankVo">
        SELECT so.short_name AS orgName,
               sca.org_id AS orgId,
               COUNT(*)  AS shortGoalCount
        FROM st_plan_activity_target scat
                 LEFT JOIN st_plan_activity sca ON scat.plan_activity_id = sca.id
                 LEFT JOIN sys_org so ON so.id = sca.org_id
        WHERE scat.update_time &gt; #{startTime}
          AND scat.update_time &lt; #{endTime}
          AND sca.org_id NOT IN ( 1, 19, 43, 65 )
          AND (scat.status = 2 OR sca.status = 2)
        GROUP BY sca.org_id
    </select>
</mapper>
