<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaCourseMapper">
    <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaCourse">
        <!--
          WARNING - @mbg.generated
        -->
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="org_id" jdbcType="INTEGER" property="orgId"/>
        <result column="child_id" jdbcType="INTEGER" property="childId"/>
        <result column="teacher_id" jdbcType="INTEGER" property="teacherId"/>
        <result column="teacher_name" jdbcType="VARCHAR" property="teacherName"/>
        <result column="plan_id" jdbcType="INTEGER" property="planId"/>
        <result column="project_min_round" jdbcType="INTEGER" property="projectMinRound" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime"/>
        <result column="end_time" jdbcType="TIMESTAMP" property="endTime"/>
        <result column="status" jdbcType="INTEGER" property="status"/>
        <result column="erp_task_id" jdbcType="INTEGER" property="erpTaskId"/>
        <result column="create_user" jdbcType="VARCHAR" property="createUser"/>
        <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
        <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
        <result column="update_user" jdbcType="VARCHAR" property="updateUser"/>
    </resultMap>

    <select id="getChildIdsByDate" resultType="java.lang.Integer">
        select distinct child_id
        from aba_course
        where status = 2
          and date_format(start_time, '%Y-%m-%d') = date_format(#{date}, '%Y-%m-%d')
    </select>
    <select id="selectReportCourseInfo" resultType="com.bmh.project.report.vo.AbaCourseVo">
        SELECT DISTINCT
            acpgd.course_id id,
            ac.teacher_name teacherName,
            acp.domain_name domainName,
            acp.project_name projectName,
            acp.project_level projectLevel,
            acpgd.short_goal_name shortGoalName,
            acpgd.record_type recordType,
            acpgd.target_result targetResult,
            acpgd.pass_rate passRate,
            ac.start_time startTime,
            ac.end_time endTime,
            scs.sign_url signUrl
        FROM
            aba_course ac
                LEFT JOIN aba_course_project_goal_data acpgd ON ac.id = acpgd.course_id
                LEFT JOIN aba_course_project acp ON acpgd.course_project_id = acp.id
                LEFT JOIN sys_course_sign scs ON ac.id = scs.course_id
        WHERE
            acpgd.child_id = #{childrenId}
          AND ac.`status` = 2
          AND ac.end_time BETWEEN #{startTime} AND #{endTime}
        ORDER BY ac.start_time,acp.domain_id,acp.project_id
    </select>
    <select id="getCourseCount" resultType="java.lang.Integer">
        SELECT COUNT(course_id)
        FROM view_analysis_course
        <where>
            <if test="orgId!=0">
                AND org_id = #{orgId}
            </if>
            <if test="0==orgId">
                AND org_id NOT IN (1,19,43,65)
            </if>
        </where>
    </select>

    <insert id="autoSignCourse">
        INSERT INTO  `sys_course_sign` (`org_id`, `parent_id`, `child_id`, `course_type`, `course_id`, `teacher_id`, `teacher_name`, `start_time`, `end_time`,  `sign_status`, `status`, `create_time`, `create_user`)
        SELECT a.`org_id`, c.`parent_id`, a.`child_id`, 1 as `course_type`, a.id as `course_id`, a.`teacher_id`, a.`teacher_name`, a.`start_time`, NOW() as `end_time`,  0 as `sign_status`, 1 as `status`, NOW() as `create_time`, '系统自动结束' as `create_user`
        FROM aba_course  a
                 INNER JOIN sys_org o on a.org_id = o.id and o.is_course_sign = 1
                 INNER JOIN ycx_children c on a.child_id = c.id
        WHERE a.status = 1
          and date(a.start_time)>=date(#{dateFrom})
    </insert>

</mapper>
