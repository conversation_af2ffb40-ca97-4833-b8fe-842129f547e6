<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.lmt.mapper.YcxLmtDomainMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.lmt.model.YcxLmtDomain">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="children_id" jdbcType="INTEGER" property="childrenId" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="domain_name" jdbcType="VARCHAR" property="domainName" />
    <result column="pass_count" jdbcType="INTEGER" property="passCount" />
    <result column="fail_count" jdbcType="INTEGER" property="failCount" />
    <result column="pass_rate" jdbcType="DOUBLE" property="passRate" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>
</mapper>