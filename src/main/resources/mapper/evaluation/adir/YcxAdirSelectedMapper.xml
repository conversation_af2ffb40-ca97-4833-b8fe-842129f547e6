<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.adir.mapper.YcxAdirSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.adir.model.YcxAdirSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="adir_id" jdbcType="INTEGER" property="adirId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />


    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="optionName" jdbcType="VARCHAR" property="optionName" />

  </resultMap>


<!--  List<YcxChatSelected> selectSelectedByType(Integer type, Integer recordId);-->
  <select id="selectSelectedByType" parameterType="Integer" resultMap="BaseResultMap" >
    select sele.*
    , adir.content
    , adir.order_num
    , op.name as optionName
    from ycx_adir_selected as sele
    inner join ycx_adir as adir
    on adir.id = sele.adir_id
    left join ycx_adir_option as op
    on op.adir_id = adir.id
    and op.value = sele.answer
    where sele.record_id = #{recordId}
    and adir.type = #{type}
    order by adir.order_num asc
  </select>


</mapper>