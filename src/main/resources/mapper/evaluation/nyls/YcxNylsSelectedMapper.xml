<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.nyls.mapper.YcxNylsSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.nyls.model.YcxNylsSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="nyls_id" jdbcType="INTEGER" property="nylsId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
    <select id="getSelecteds" resultMap="BaseResultMap">
      select sele.*,
      nyls.content,
      nyls.order_num,
      op.name as optionName
      from
        ycx_nyls_selected as sele
          inner join
        ycx_nyls as nyls
        on
          nyls.id = sele.nyls_id
          left join
        ycx_nyls_option as op
        on
            op.nyls_id = nyls.id
            and
            op.value = sele.answer
      where
        sele.record_id = #{recordId}
      order by nyls.order_num asc
    </select>
</mapper>