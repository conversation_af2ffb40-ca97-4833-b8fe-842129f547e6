package com.bmh.project.evaluation.conners.service;

import com.bmh.common.base.BaseService;
import com.bmh.project.evaluation.conners.model.YcxConnersTrs;

import java.util.List;

/**
 * ycx_conners_trs表对应的Service接口
 *
 * <AUTHOR>
 * @date 2021年07月10日 14:43:46
 */
public interface YcxConnersTrsService extends BaseService<YcxConnersTrs> {

    /**
     * 根据类型获取问题
     * @param type 类型(1品行问题，2多动，3注意力不集中-被动， 4其他)
     * @return
     */
    List<YcxConnersTrs> getListByType(Integer type);
}