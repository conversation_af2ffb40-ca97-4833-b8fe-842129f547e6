<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.iat.mapper.YcxIatSelectedMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.iat.model.YcxIatSelected">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="record_id" jdbcType="INTEGER" property="recordId" />
    <result column="iat_id" jdbcType="INTEGER" property="iatId" />
    <result column="answer" jdbcType="VARCHAR" property="answer" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="updat_time" jdbcType="TIMESTAMP" property="updatTime" />
  </resultMap>

    <select id="getSelecteds" parameterType="Integer" resultMap="BaseResultMap">
      select
        sele.*,
        iat.content,
        iat.order_num,
        op.name as optionName
      from
        ycx_iat_selected as sele
          inner join
        ycx_iat as iat
        on
          iat.id = sele.iat_id
          left join
        ycx_iat_option as op
        on
            op.iat_id = iat.id
            and
            op.value = sele.answer
      where
        sele.record_id = #{recordId}
      order by iat.order_num asc
    </select>
</mapper>