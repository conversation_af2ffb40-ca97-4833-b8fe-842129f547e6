<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.evaluation.weiss.mapper.YcxWeissMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.evaluation.weiss.model.YcxWeiss">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="order_num" jdbcType="INTEGER" property="orderNum" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />

    <collection property="options" ofType="com.bmh.project.evaluation.weiss.model.YcxWeissOption">
      <result column="weiss_id" property="weissId"/>
      <result column="name" property="name"/>
      <result column="value" property="value"/>
      <result column="score" property="score"/>
    </collection>
  </resultMap>

  <select id="getWeissListByType" resultMap="BaseResultMap">
    select weiss.*
    , op.weiss_id
    , op.name
    , op.value
    , op.score
    from ycx_weiss as weiss
    inner join ycx_weiss_option as op
    on op.weiss_id = weiss.id
    where weiss.type = #{type}
    and weiss.status = 1
    order by weiss.order_num asc, op.id asc
  </select>
</mapper>