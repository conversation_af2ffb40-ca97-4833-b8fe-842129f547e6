package com.bmh.project.evaluation.behavior.model;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
import lombok.Data;

@Data
@Table(name = "ycx_behavior")
public class YcxBehavior implements Serializable {
    @Id
    @Column(name = "id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    /**
     * 类型(1大运动，2精细动作，3适应能力，4语言，5社会行为)
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 月龄
     */
    @Column(name = "month")
    private Integer month;

    /**
     * 标题
     */
    @Column(name = "title")
    private String title;

    /**
     * 提示
     */
    @Column(name = "tips")
    private String tips;

    /**
     * 内容
     */
    @Column(name = "content")
    private String content;

    /**
     * 要求
     */
    @Column(name = "requirements")
    private String requirements;

    /**
     * 示例图片
     */
    @Column(name = "example_img")
    private String exampleImg;

    /**
     * 道具
     */
    @Column(name = "props")
    private String props;

    /**
     * 状态(0无效，1有效)
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 排序
     */
    @Column(name = "order_num")
    private Integer orderNum;


    /**
     * 是否占位项
     */
    @Column(name = "is_blank")
    private Integer isBlank;


    /**
     * 分数
     */
    @Column(name = "item_score")
    private String itemScore;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    private static final long serialVersionUID = 1L;





}