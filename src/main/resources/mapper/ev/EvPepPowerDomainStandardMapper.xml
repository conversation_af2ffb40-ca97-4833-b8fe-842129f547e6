<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.ev.mapper.EvPepPowerDomainStandardMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.ev.model.EvPepPowerDomainStandard">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="min_age" jdbcType="INTEGER" property="minAge" />
    <result column="max_age" jdbcType="INTEGER" property="maxAge" />
    <result column="domain_id" jdbcType="INTEGER" property="domainId" />
    <result column="power_domain" jdbcType="VARCHAR" property="powerDomain" />
    <result column="raw_score" jdbcType="INTEGER" property="rawScore" />
    <result column="dev_age" jdbcType="INTEGER" property="devAge" />
    <result column="dev_age_content" jdbcType="VARCHAR" property="devAgeContent" />
    <result column="percentage_series" jdbcType="VARCHAR" property="percentageSeries" />
    <result column="adapt_level" jdbcType="VARCHAR" property="adaptLevel" />
    <result column="standard_score" jdbcType="INTEGER" property="standardScore" />
    <result column="is_delete" jdbcType="INTEGER" property="isDelete" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="update_by" jdbcType="VARCHAR" property="updateBy" />
  </resultMap>



</mapper>
