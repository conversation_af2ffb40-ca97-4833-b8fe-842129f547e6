<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.aba.mapper.AbaAbnormalSupervisionMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.aba.model.AbaAbnormalSupervision">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="org_id" jdbcType="INTEGER" property="orgId" />
    <result column="plan_id" jdbcType="INTEGER" property="planId" />
    <result column="child_id" jdbcType="INTEGER" property="childId" />
    <result column="plan_project_id" jdbcType="INTEGER" property="planProjectId" />
    <result column="abnormal_date" jdbcType="DATE" property="abnormalDate" />
    <result column="abnormal_badge" jdbcType="VARCHAR" property="abnormalBadge" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <insert id="generateAbnormalLog">
      INSERT INTO aba_abnormal_supervision (org_id, plan_id, child_id, plan_project_id, abnormal_date, abnormal_badge,
                                            create_time)
      SELECT distinct app.org_id,
                      app.plan_id,
                      app.child_id,
                      app.id                                                           AS plan_project_id,
                      CURRENT_DATE                                                     AS abnormal_date,
                      CONCAT_WS(',', rs.is_continuous_pass, rs.is_fail, rs.is_slow) AS abnormal_badge,
                      CURRENT_TIME                                                     AS create_time
      FROM aba_plan_project app
               LEFT JOIN aba_plan ap ON app.plan_id = ap.id
               LEFT JOIN aba_plan_project_goal gl ON app.id = gl.plan_project_id and gl.status=1
               LEFT JOIN aba_plan_project_goal_data rs ON gl.id = rs.plan_project_goal_id and rs.`status`=1
      WHERE app.`status` = 1
        AND ap.`status` = 1
        AND (
               rs.is_continuous_pass = 1
              OR rs.is_fail = 1
              OR rs.is_slow = 1
              OR rs.is_step_pass = 1)
  </insert>
</mapper>