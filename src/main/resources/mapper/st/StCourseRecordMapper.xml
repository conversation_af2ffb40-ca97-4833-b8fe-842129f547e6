<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bmh.project.st.mapper.StCourseRecordMapper">
  <resultMap id="BaseResultMap" type="com.bmh.project.st.model.StCourseRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="course_id" jdbcType="INTEGER" property="courseId" />
    <result column="course_activity_id" jdbcType="INTEGER" property="courseActivityId" />
    <result column="course_target_id" jdbcType="INTEGER" property="courseTargetId" />
    <result column="support_type" jdbcType="INTEGER" property="supportType" />
    <result column="all_support_type" jdbcType="VARCHAR" property="allSupportType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
  </resultMap>

  <select id="getRecent14Data" resultType="com.bmh.project.st.vo.StCourseRecordStatisticsVo">
      select scr.id,
             scr.course_id as courseId,
             scr.course_activity_id as courseActivityId,
             scr.course_target_id as courseTargetId,
             scr.support_type as supportType,
             scr.all_support_type as allSupportType,
             scr.remark,
             scr.status
      from st_course_activity_target scat
               left join st_course_record scr on scat.id = scr.course_target_id
      where scat.plan_activity_target_id = #{planActivityTargetId}
        and scat.is_done = 1
      order by scr.create_time desc
      limit 14
  </select>
    <select id="getRecordCount" resultType="java.lang.Integer">
        SELECT COUNT(scr.id)
        FROM st_course_record scr
        LEFT JOIN st_course sc ON scr.course_id = sc.id
        WHERE scr.status = 1
          AND scr.create_time &gt; #{startTime}
          AND scr.create_time &lt; #{endTime}
          <if test="orgId!=0">
            AND sc.org_id = #{orgId}
          </if>
        <if test="0==orgId">
            AND sc.org_id NOT IN (1,19,43,65)
        </if>
    </select>
</mapper>
